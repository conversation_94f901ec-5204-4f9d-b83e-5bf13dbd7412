# 🎨 WSTC UI設計器

## 📁 資料夾結構

```
wstc_new/
├── ui_designer/                    # UI設計器專用資料夾
│   ├── ui_designer.py             # 主設計器程式
│   ├── start_ui_designer.py       # 設計器內部啟動腳本
│   ├── demo_ui_design.py          # 示範UI程式
│   ├── UI_DESIGNER_GUIDE.md       # 詳細使用指南
│   ├── UI_DESIGNER_README.md      # 功能概覽文件
│   └── README.md                  # 本文件
├── start_ui_designer.py           # 根目錄啟動腳本
├── start_ui_designer.ps1          # PowerShell啟動腳本
└── requirements.txt                # 依賴套件清單
```

## 🚀 啟動方式

### 方法1: 使用根目錄啟動腳本 (推薦)
```bash
# 在 wstc_new 根目錄執行
python start_ui_designer.py
```

### 方法2: 使用PowerShell腳本 (Windows)
```powershell
# 在 wstc_new 根目錄執行
.\start_ui_designer.ps1
```

### 方法3: 直接執行設計器
```bash
# 在 ui_designer 資料夾內執行
cd ui_designer
python start_ui_designer.py
```

## 🎯 功能特色

- ✅ **拖拉式設計**: 從控件庫拖拉控件到畫布
- ✅ **即時編輯**: 選中控件即時編輯屬性
- ✅ **程式碼生成**: 自動生成整合系統的PyQt5程式碼
- ✅ **檔案管理**: 儲存/載入設計檔案 (JSON格式)
- ✅ **UI預覽**: 即時預覽設計效果
- ✅ **系統整合**: 自動整合WSTC數據流

## 🛠️ 支援的控件

| 控件類型 | 說明 | 狀態 |
|----------|------|------|
| QLabel | 文字標籤 | ✅ |
| QPushButton | 按鈕 | ✅ |
| QLineEdit | 單行輸入框 | ✅ |
| QTextEdit | 多行文字編輯器 | ✅ |
| QComboBox | 下拉選單 | ✅ |
| QCheckBox | 勾選框 | ✅ |
| QSpinBox | 數字輸入框 | ✅ |
| QSlider | 滑桿控件 | ✅ |
| QProgressBar | 進度條 | ✅ |

## 📋 使用流程

1. **啟動設計器** → 選擇任一啟動方式
2. **添加控件** → 從左側拖拉控件到畫布
3. **編輯屬性** → 點選控件，右側編輯屬性
4. **調整佈局** → 拖拉移動，自動網格對齊
5. **儲存設計** → 檔案 → 儲存 (JSON格式)
6. **生成程式碼** → 檔案 → 生成程式碼
7. **預覽效果** → 檢視 → 預覽

## 🔧 系統整合

生成的UI程式碼自動包含：
- `get_data_manager()` 數據管理器
- `sensor_queue` 感測器數據隊列
- `update_data()` 數據更新方法
- 完整的WSTC系統模組導入

## 📝 示範程式

```bash
# 執行示範UI
python demo_ui_design.py
```

示範程式展示了：
- 感測器數據顯示
- 即時狀態監控
- 系統整合功能
- 數據流更新機制

## 🎨 設計檔案

- **儲存格式**: JSON
- **檔案位置**: 可自訂儲存位置
- **版本控制**: 支援設計歷史追蹤
- **匯入匯出**: 完整的設計檔案管理

## 💡 最佳實踐

1. **命名規範**: 使用有意義的控件名稱
2. **佈局規劃**: 先規劃整體佈局再添加細節
3. **屬性設定**: 合理設定控件屬性提升使用體驗
4. **程式碼整合**: 生成程式碼後根據需求調整業務邏輯
5. **定期儲存**: 設計過程中定期儲存避免丟失

---

**🎨 開始設計您的專業UI界面！**

使用WSTC UI設計器，讓界面設計變得簡單而高效。 