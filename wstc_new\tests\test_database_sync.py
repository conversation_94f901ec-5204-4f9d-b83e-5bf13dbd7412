#!/usr/bin/env python3
"""
測試資料庫同步器功能
"""
import sys
import os
import time
import threading

# 添加父目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_database_sync_import():
    """測試資料庫同步器導入"""
    try:
        from database_sync import DatabaseSynchronizer
        print("✓ 資料庫同步器導入成功")
        return True
    except ImportError as e:
        print(f"✗ 資料庫同步器導入失敗: {e}")
        return False
    except Exception as e:
        print(f"✗ 資料庫同步器導入錯誤: {e}")
        return False

def test_database_sync_creation():
    """測試資料庫同步器創建"""
    try:
        from database_sync import DatabaseSynchronizer
        
        synchronizer = DatabaseSynchronizer()
        print("✓ 資料庫同步器創建成功")
        print(f"  - Box ID: {synchronizer.box_id}")
        print(f"  - 同步間隔: {synchronizer.sync_interval} 秒")
        return True
        
    except Exception as e:
        print(f"✗ 資料庫同步器創建失敗: {e}")
        return False

def test_database_sync_config():
    """測試資料庫同步器配置"""
    try:
        from database_sync import DatabaseSynchronizer
        
        synchronizer = DatabaseSynchronizer()
        
        # 測試本地配置
        if synchronizer.local_config:
            print("✓ 本地資料庫配置已載入")
            print(f"  - 主機: {synchronizer.local_config.get('host', 'N/A')}")
            print(f"  - 資料庫: {synchronizer.local_config.get('database', 'N/A')}")
        else:
            print("⚠️ 本地資料庫配置未載入")
        
        return True
        
    except Exception as e:
        print(f"✗ 資料庫同步器配置測試失敗: {e}")
        return False

def test_database_sync_initialization():
    """測試資料庫同步器初始化"""
    try:
        from database_sync import DatabaseSynchronizer
        
        synchronizer = DatabaseSynchronizer()
        
        print("正在測試初始化...")
        result = synchronizer.initialize()
        
        if result:
            print("✓ 資料庫同步器初始化成功")
            if synchronizer.remote_config:
                print("✓ 遠端資料庫配置已載入")
                print(f"  - 主機: {synchronizer.remote_config.get('host', 'N/A')}")
                print(f"  - 資料庫: {synchronizer.remote_config.get('database', 'N/A')}")
            return True
        else:
            print("⚠️ 資料庫同步器初始化失敗（可能是資料庫連接問題）")
            return False
        
    except Exception as e:
        print(f"✗ 資料庫同步器初始化測試失敗: {e}")
        return False

def test_database_sync_start_stop():
    """測試資料庫同步器啟動和停止"""
    try:
        from database_sync import DatabaseSynchronizer
        
        synchronizer = DatabaseSynchronizer()
        
        print("正在測試啟動...")
        start_result = synchronizer.start()
        
        if start_result:
            print("✓ 資料庫同步器啟動成功")
            
            # 運行一小段時間
            time.sleep(2)
            
            print("正在測試停止...")
            synchronizer.stop()
            print("✓ 資料庫同步器停止成功")
            return True
        else:
            print("⚠️ 資料庫同步器啟動失敗（可能是資料庫連接問題）")
            return False
        
    except Exception as e:
        print(f"✗ 資料庫同步器啟動/停止測試失敗: {e}")
        return False

def test_integration_with_system():
    """測試與系統的整合"""
    try:
        from shared_data import get_data_manager
        
        data_manager = get_data_manager()
        
        # 檢查系統狀態中是否有同步器狀態
        if 'db_sync_active' in data_manager.system_status:
            print("✓ 系統狀態包含資料庫同步器狀態")
        else:
            print("⚠️ 系統狀態未包含資料庫同步器狀態")
        
        # 檢查心跳記錄中是否有同步器
        if 'db_sync' in data_manager.heartbeats:
            print("✓ 心跳記錄包含資料庫同步器")
        else:
            print("⚠️ 心跳記錄未包含資料庫同步器")
        
        return True
        
    except Exception as e:
        print(f"✗ 系統整合測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("資料庫同步器功能測試")
    print("=" * 60)
    
    tests = [
        ("導入測試", test_database_sync_import),
        ("創建測試", test_database_sync_creation),
        ("配置測試", test_database_sync_config),
        ("初始化測試", test_database_sync_initialization),
        ("啟動/停止測試", test_database_sync_start_stop),
        ("系統整合測試", test_integration_with_system),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name} 未完全通過")
        except Exception as e:
            print(f"✗ {test_name} 發生異常: {e}")
    
    print("\n" + "=" * 60)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("✓ 所有測試通過！資料庫同步器已準備就緒")
        return True
    else:
        print("⚠️ 部分測試未通過，可能是資料庫連接問題")
        print("注意：在沒有實際資料庫的環境中，部分測試失敗是正常的")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
