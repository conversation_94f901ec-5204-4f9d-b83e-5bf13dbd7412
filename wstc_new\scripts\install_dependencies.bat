@echo off
REM 高效能實時感測器系統 - Windows 依賴安裝腳本

echo ==========================================
echo 安裝高效能實時感測器系統依賴 (Windows)
echo ==========================================

REM 檢查 Python 版本
echo 檢查 Python 版本...
python --version
if %ERRORLEVEL% neq 0 (
    echo 錯誤: Python 未安裝或未加入 PATH
    echo 請從 https://www.python.org 下載並安裝 Python 3.7+
    pause
    exit /b 1
)

REM 檢查 pip
echo 檢查 pip...
python -m pip --version
if %ERRORLEVEL% neq 0 (
    echo 錯誤: pip 未安裝
    echo 請重新安裝 Python 並確保勾選 "Add Python to PATH"
    pause
    exit /b 1
)

REM 升級 pip
echo 升級 pip...
python -m pip install --upgrade pip

REM 安裝 Visual C++ Build Tools (如果需要)
echo.
echo 注意: 如果安裝過程中出現編譯錯誤，請安裝:
echo "Microsoft C++ Build Tools" 或 "Visual Studio Community"
echo 下載地址: https://visualstudio.microsoft.com/visual-cpp-build-tools/
echo.

REM 安裝 Python 依賴
echo 安裝 Python 依賴...
python -m pip install -r requirements.txt

if %ERRORLEVEL% neq 0 (
    echo.
    echo 某些依賴安裝失敗，嘗試逐一安裝...
    
    REM 核心依賴
    echo 安裝核心依賴...
    python -m pip install pymysql>=1.0.2
    python -m pip install pyserial>=3.5
    python -m pip install requests>=2.28.0
    
    REM 類型支援
    echo 安裝類型支援...
    python -m pip install typing-extensions>=4.0.0
    
    REM 跨平台輸入
    echo 安裝跨平台輸入支援...
    python -m pip install pynput>=1.7.6
    
    REM 可選依賴
    echo 安裝可選依賴...
    python -m pip install numpy>=1.21.0
)

REM 驗證安裝
echo ==========================================
echo 驗證安裝...
echo ==========================================

echo 檢查 Python 版本:
python --version

echo.
echo 檢查關鍵模組:
python -c "import pymysql; print('✓ pymysql 已安裝')" 2>nul || echo ✗ pymysql 安裝失敗
python -c "import serial; print('✓ pyserial 已安裝')" 2>nul || echo ✗ pyserial 安裝失敗
python -c "import requests; print('✓ requests 已安裝')" 2>nul || echo ✗ requests 安裝失敗
python -c "import PyQt5; print('✓ PyQt5 已安裝')" 2>nul || echo ✗ PyQt5 安裝失敗
python -c "import pynput; print('✓ pynput 已安裝')" 2>nul || echo ✗ pynput 安裝失敗
python -c "import typing_extensions; print('✓ typing-extensions 已安裝')" 2>nul || echo ⚠ typing-extensions 未安裝

echo.
echo 檢查可選模組:
python -c "import numpy; print('✓ numpy 已安裝')" 2>nul || echo ⚠ numpy 未安裝

REM 檢查 COM 埠（Windows）
echo.
echo 檢查可用的 COM 埠:
python -c "import serial.tools.list_ports; ports = serial.tools.list_ports.comports(); [print(f'  {port.device} - {port.description}') for port in ports] if ports else print('  無可用 COM 埠')"

echo ==========================================
echo 依賴安裝完成！
echo ==========================================

echo 下一步:
echo 1. 編輯 config.ini 設置資料庫和 COM 埠
echo 2. 確保 MySQL 服務正在運行
echo 3. 連接感測器設備到正確的 COM 埠
echo 4. 執行: python main.py
echo.
echo 注意事項:
echo - Windows 防火牆可能會詢問網路存取權限，請允許
echo - 某些防毒軟體可能會阻擋程式，請加入白名單
echo - COM 埠號碼需要在 config.ini 中正確設置

pause