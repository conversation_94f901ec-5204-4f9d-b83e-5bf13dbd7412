# WSTC 項目文件清單

## 📁 根目錄 - 主要運行程式

### 🚀 啟動程式
| 文件 | 用途 | 推薦度 |
|------|------|--------|
| `main_simple.py` | 簡化啟動程式（線程版本） | ⭐⭐⭐⭐⭐ |
| `main.py` | 完整啟動程式（多進程版本） | ⭐⭐⭐ |
| `start.ps1` | PowerShell 快速啟動腳本 | ⭐⭐⭐⭐ |
| `start.bat` | 批次檔快速啟動腳本 | ⭐⭐⭐ |
| `start.sh` | Linux 快速啟動腳本 | ⭐⭐⭐⭐ |

### 🎯 核心模組
| 文件 | 用途 | 說明 |
|------|------|------|
| `main_ui.py` | Qt5 用戶界面 | 主要 UI 界面，包含所有顯示和交互功能 |
| `sensor_manager.py` | 感測器管理器 | 處理扭力、電壓、RFID 等感測器數據 |
| `database_logger.py` | 資料庫記錄器 | 負責數據持久化和資料庫操作 |
| `database_sync.py` | 資料庫同步器 | 從遠端資料庫同步 MAPPING 和 TEST_MEMBER 資料 |
| `shared_data.py` | 共享數據管理 | 進程間通信和數據共享 |
| `config.py` | 配置管理 | 系統配置讀取和管理 |

### 🔧 其他核心文件
| 文件 | 用途 | 說明 |
|------|------|------|
| `requirements.txt` | Python 依賴清單 | 生產環境依賴 |
| `DB_update.py` | 資料庫更新工具 | 資料庫結構更新和維護 |
| `FA_RDB_OP.py` | 資料庫操作工具 | 資料庫查詢和操作功能 |

### 📋 說明文件
| 文件 | 用途 |
|------|------|
| `README.md` | 項目主要說明文檔 |
| `PROJECT_STRUCTURE.md` | 項目結構詳細說明 |
| `FILE_MANIFEST.md` | 文件清單（本文件） |

## 📁 configs/ - 配置文件

| 文件 | 用途 | 說明 |
|------|------|------|
| `config.ini` | 主配置文件 | 生產環境使用的配置 |
| `config_demo.ini` | 演示配置文件 | 演示模式和測試用配置 |
| `requirements-dev.txt` | 開發依賴清單 | 開發環境額外依賴 |

## 📁 tests/ - 測試腳本

| 文件 | 用途 | 說明 |
|------|------|------|
| `test_qt5.py` | PyQt5 功能測試 | 驗證 PyQt5 安裝和基本功能 |
| `test_startup_flow.py` | 啟動流程測試 | 模擬和測試系統啟動流程 |
| `test_database_sync.py` | 資料庫同步器測試 | 測試資料庫同步器功能和整合 |

## 📁 scripts/ - 安裝和構建腳本

| 文件 | 用途 | 說明 |
|------|------|------|
| `install_dependencies.bat` | Windows 依賴安裝 | 自動安裝 Windows 系統依賴 |
| `install_dependencies.sh` | Linux 依賴安裝 | 自動安裝 Linux 系統依賴 |
| `build_exe.bat` | Windows 打包腳本 | 將 Python 程式打包為 exe |
| `build_exe.py` | 打包配置 | PyInstaller 打包配置 |
| `start_system.sh` | 系統啟動腳本 | Linux 系統服務啟動 |
| `stop_system.sh` | 系統停止腳本 | Linux 系統服務停止 |

## 📁 docs/ - 文檔說明

| 文件 | 用途 | 說明 |
|------|------|------|
| `QT5_MIGRATION_README.md` | Qt5 遷移說明 | 從 tkinter 到 Qt5 的詳細遷移文檔 |
| `STARTUP_FLOW_README.md` | 啟動流程說明 | 技術層面的啟動流程文檔 |
| `CONVERSION_SUMMARY.md` | 轉換總結 | Qt5 轉換工作的完整總結 |
| `STARTUP_GUIDE.md` | 啟動指南 | 用戶啟動指南和故障排除 |
| `README.md` | 原始說明文檔 | 舊版本的說明文檔 |
| `打包說明.md` | 打包說明 | 程式打包和部署說明 |
| `系統架構圖.md` | 系統架構圖 | 系統架構和組件關係圖 |

## 📁 legacy/ - 舊版本備份

| 文件 | 用途 | 說明 |
|------|------|------|
| `insert_ui.py` | 舊版 UI 文件 | tkinter 版本的 UI 文件備份 |
| `DB_update.py` | 舊版資料庫同步 | 原始的資料庫同步程式備份 |

## 使用建議

### 🎯 日常使用
1. **Windows**: 使用 `start.ps1` 或 `start.bat`
2. **Linux**: 使用 `start.sh`
3. **開發**: 直接運行 `python main_simple.py`

### 🔧 開發維護
1. **配置修改**: 編輯 `configs/config.ini`
2. **功能測試**: 運行 `tests/` 中的測試腳本
3. **依賴安裝**: 使用 `scripts/` 中的安裝腳本

### 📚 文檔查閱
1. **快速開始**: 查看 `README.md`
2. **詳細說明**: 查看 `docs/` 資料夾
3. **項目結構**: 查看 `PROJECT_STRUCTURE.md`

### 🗄️ 版本管理
1. **新功能**: 在根目錄開發
2. **舊版本**: 移至 `legacy/` 保存
3. **配置備份**: 在 `configs/` 中保存多個版本

## 文件大小統計

### 核心程式文件
- `main_ui.py`: ~25KB (主要 UI 邏輯)
- `sensor_manager.py`: ~15KB (感測器處理)
- `database_logger.py`: ~12KB (資料庫操作)
- `database_sync.py`: ~10KB (資料庫同步)
- `shared_data.py`: ~8KB (數據共享)
- `config.py`: ~6KB (配置管理)

### 總計
- **核心程式**: ~80KB
- **配置文件**: ~5KB
- **測試腳本**: ~20KB
- **文檔說明**: ~50KB
- **安裝腳本**: ~10KB

## 維護檢查清單

### ✅ 每次更新後檢查
- [ ] 核心程式是否在根目錄
- [ ] 配置文件是否在 configs/
- [ ] 測試腳本是否可正常運行
- [ ] 文檔是否已更新

### ✅ 部署前檢查
- [ ] 運行所有測試腳本
- [ ] 檢查依賴是否完整
- [ ] 驗證配置文件正確性
- [ ] 確認啟動腳本可用

---

**文件清單版本**: 2.0  
**最後更新**: 2025-08-01  
**總文件數**: 30+ 個文件  
**資料夾數**: 5 個分類資料夾
