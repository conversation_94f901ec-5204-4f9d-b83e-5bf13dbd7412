# WSTC 高效能測試系統 - PowerShell 啟動腳本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "WSTC 高效能測試系統 - 快速啟動" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host

# 檢查 Python 環境
Write-Host "檢查 Python 環境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✓ $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ 錯誤: 未找到 Python，請先安裝 Python 3.6+" -ForegroundColor Red
    Read-Host "按 Enter 鍵退出"
    exit 1
}

# 檢查 PyQt5 安裝
Write-Host "檢查 PyQt5 安裝..." -ForegroundColor Yellow
try {
    python -c "import PyQt5" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ PyQt5 已安裝" -ForegroundColor Green
    } else {
        throw "PyQt5 not found"
    }
} catch {
    Write-Host "⚠️ PyQt5 未安裝，正在安裝..." -ForegroundColor Yellow
    pip install PyQt5
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ PyQt5 安裝失敗" -ForegroundColor Red
        Read-Host "按 Enter 鍵退出"
        exit 1
    }
}

Write-Host
Write-Host "選擇啟動模式:" -ForegroundColor Cyan
Write-Host "1. 簡化模式 (推薦，線程版本)" -ForegroundColor White
Write-Host "2. 完整模式 (多進程版本)" -ForegroundColor White
Write-Host "3. 測試模式 (功能測試)" -ForegroundColor White
Write-Host "4. 配置管理 (工站配置)" -ForegroundColor White
Write-Host

$choice = Read-Host "請選擇 (1-4)"

switch ($choice) {
    "1" {
        Write-Host "啟動簡化模式..." -ForegroundColor Green
        python main_simple.py
    }
    "2" {
        Write-Host "啟動完整模式..." -ForegroundColor Green
        python main.py
    }
    "3" {
        Write-Host "運行測試..." -ForegroundColor Green
        python tests/test_qt5.py
        Read-Host "按 Enter 繼續"
        python tests/test_startup_flow.py
    }
    "4" {
        Write-Host "配置管理..." -ForegroundColor Green
        python tools/config_manager.py status
        Read-Host "按 Enter 繼續"
    }
    default {
        Write-Host "無效選擇，使用默認簡化模式..." -ForegroundColor Yellow
        python main_simple.py
    }
}

Write-Host
Write-Host "系統已關閉" -ForegroundColor Cyan
Read-Host "按 Enter 鍵退出"
