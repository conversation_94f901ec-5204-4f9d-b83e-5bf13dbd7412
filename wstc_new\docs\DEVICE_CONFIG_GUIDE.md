# 設備序號配置指南

## 概述

WSTC 系統現在支援多台設備的序號配置，每台設備可以有獨立的配置文件，包含不同的工站序號、串口設置等。

## 功能特色

- ✅ **多工站支援**: 支援無限數量的工站配置
- ✅ **動態序號顯示**: UI 自動顯示當前工站序號
- ✅ **配置管理工具**: 簡單的命令行工具管理配置
- ✅ **自動備份**: 切換配置時自動備份當前配置
- ✅ **資料庫同步整合**: 自動使用對應的 box_id

## 配置文件結構

### 設備配置區段
```ini
[Device]
# 設備序號配置
station_id = 3          # 工站序號（顯示在 UI 上）
station_name = 工站3     # 工站名稱
box_id = 3              # 資料庫同步使用的 Box ID
```

### 完整配置範例
```ini
[LocalDatabase]
local_host = localhost
local_user = power
local_password = 1q2w3e4r
local_database = WSTC

[SerialPorts_Windows]
torque_port = COM1
voltage_port = COM4
rfid_port = COM2
torque_baudrate = 115200
voltage_baudrate = 9600
rfid_baudrate = 9600

[Device]
station_id = 3
station_name = 工站3
box_id = 3

[TorqueSettings]
data_filter_enabled = true
min_torque_change = 0.1
max_torque_value = 1000
torque_timeout = 5.0
```

## 使用方法

### 1. 查看當前狀態
```bash
python config_manager.py status
```
輸出：
```
==================================================
WSTC 配置管理器
==================================================
當前工站: #3
活動配置: configs\config.ini

可用配置:
  工站1: configs\config_station1.ini
  工站2: configs\config_station2.ini
  工站3: configs\config_station3.ini (當前)
```

### 2. 切換工站配置
```bash
python config_manager.py switch 1
```
輸出：
```
已備份當前配置到: configs\config_backup.ini
已切換到工站1配置
配置切換完成！請重新啟動系統以使配置生效。
```

### 3. 創建新工站配置
```bash
python config_manager.py create 4
```
或指定工站名稱：
```bash
python config_manager.py create 4 "測試工站"
```

### 4. 列出所有可用配置
```bash
python config_manager.py list
```

## 配置文件命名規則

- **活動配置**: `configs/config.ini` - 系統實際使用的配置
- **工站配置**: `configs/config_station{N}.ini` - 各工站的配置模板
- **備份配置**: `configs/config_backup.ini` - 切換前的備份

## 部署流程

### 新設備部署
1. **複製系統文件**到新設備
2. **創建工站配置**：
   ```bash
   python config_manager.py create 5 "生產線A工站5"
   ```
3. **切換到新配置**：
   ```bash
   python config_manager.py switch 5
   ```
4. **修改特定設置**（如串口號）：
   編輯 `configs/config_station5.ini`
5. **啟動系統**：
   ```bash
   python main_simple.py
   ```

### 現有設備配置
1. **查看當前狀態**：
   ```bash
   python config_manager.py status
   ```
2. **創建當前配置的工站版本**：
   ```bash
   python config_manager.py create 3
   ```
3. **驗證配置**：
   ```bash
   python tests/test_device_config.py
   ```

## 配置項目說明

### Device 區段
| 參數 | 說明 | 範例 | 用途 |
|------|------|------|------|
| `station_id` | 工站序號 | `3` | UI 顯示 `#3` |
| `station_name` | 工站名稱 | `工站3` | 識別和管理用 |
| `box_id` | 資料庫 Box ID | `3` | 資料庫同步使用 |

### SerialPorts 區段
不同工站可能使用不同的串口：
```ini
# 工站1
[SerialPorts_Windows]
torque_port = COM1
voltage_port = COM4
rfid_port = COM2

# 工站2  
[SerialPorts_Windows]
torque_port = COM5
voltage_port = COM6
rfid_port = COM7
```

## 系統整合

### UI 顯示
- 工站序號自動顯示在 UI 左上角
- 格式：`#N`（N 為 station_id）
- 從配置文件動態讀取

### 資料庫同步
- 自動使用配置的 `box_id`
- 同步 MAPPING 和 TEST_MEMBER 表
- 支援多工站獨立同步

### 日誌記錄
- 所有操作都有詳細日誌
- 包含工站識別信息
- 便於問題追蹤

## 故障排除

### 1. 配置文件不存在
**錯誤**: `工站X的配置文件不存在`
**解決**: 
```bash
python config_manager.py create X
```

### 2. 配置讀取失敗
**錯誤**: `設備配置讀取失敗`
**解決**: 
1. 檢查 `configs/config.ini` 是否存在
2. 檢查 `[Device]` 區段是否完整
3. 運行測試：`python tests/test_device_config.py`

### 3. UI 顯示錯誤序號
**解決**:
1. 確認配置文件正確
2. 重新啟動系統
3. 檢查配置管理器狀態

## 最佳實踐

### 1. 配置管理
- 為每台設備創建專用配置文件
- 定期備份重要配置
- 使用有意義的工站名稱

### 2. 部署流程
- 先在測試環境驗證配置
- 使用配置管理工具切換
- 記錄每次配置變更

### 3. 維護建議
- 定期檢查配置文件完整性
- 保持配置文件版本同步
- 建立配置變更記錄

## 進階功能

### 批量配置創建
```bash
# 創建多個工站配置
for i in {1..10}; do
    python config_manager.py create $i "生產線A工站$i"
done
```

### 配置驗證
```bash
# 測試所有配置功能
python tests/test_device_config.py
```

### 自動化部署
可以編寫腳本自動化配置部署：
```bash
#!/bin/bash
STATION_ID=$1
STATION_NAME=$2

python config_manager.py create $STATION_ID "$STATION_NAME"
python config_manager.py switch $STATION_ID
python tests/test_device_config.py
```

---

**版本**: 2.0  
**更新日期**: 2025-08-04  
**狀態**: ✅ 已實現並測試
