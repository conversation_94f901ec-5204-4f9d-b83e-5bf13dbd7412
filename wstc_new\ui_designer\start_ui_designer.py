"""
UI設計器啟動腳本
快速啟動WSTC系統的UI設計器
"""
import sys
import os

# 添加當前目錄和父目錄到Python路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 父目錄 (wstc_new)
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

try:
    from ui_designer import main
    
    if __name__ == "__main__":
        print("正在啟動WSTC UI設計器...")
        print("支援功能：")
        print("✅ 拖拉式控件設計")
        print("✅ 屬性即時編輯")
        print("✅ 程式碼自動生成")
        print("✅ UI預覽功能")
        print("✅ 設計檔案儲存/載入")
        print("-" * 40)
        
        main()
        
except ImportError as e:
    print(f"❌ 導入錯誤: {e}")
    print("請確保所有依賴套件已安裝：")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ 啟動失敗: {e}")
    sys.exit(1)