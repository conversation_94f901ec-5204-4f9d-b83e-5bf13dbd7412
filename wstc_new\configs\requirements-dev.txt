# 開發環境額外依賴
# 包含所有生產環境依賴 + 開發工具

# 引入基礎依賴
-r requirements.txt

# ===== 測試框架 =====
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.7.0
pytest-asyncio>=0.21.0

# ===== 程式碼品質 =====
black>=22.0.0               # 程式碼格式化
flake8>=5.0.0               # 程式碼檢查
isort>=5.10.0               # import 排序
mypy>=0.991                 # 類型檢查
bandit>=1.7.0               # 安全檢查

# ===== 文檔生成 =====
sphinx>=5.0.0               # 文檔生成
sphinx-rtd-theme>=1.0.0     # ReadTheDocs 主題

# ===== 效能分析 =====
cProfile                    # Python 內建效能分析
memory-profiler>=0.60.0     # 記憶體分析
psutil>=5.9.0               # 系統資源監控
line-profiler>=4.0.0        # 行級效能分析

# ===== 除錯工具 =====
pdb                         # Python 內建除錯器
ipdb>=0.13.0                # 增強型除錯器
pudb>=2022.1                # 全屏除錯器

# ===== 程式碼分析 =====
pylint>=2.15.0              # 深度程式碼分析
radon>=5.1.0                # 複雜度分析
vulture>=2.6.0              # 死代碼檢測

# ===== 開發輔助 =====
pre-commit>=2.20.0          # Git hook 管理
tox>=3.26.0                 # 多環境測試
wheel>=0.37.0               # 打包工具

# ===== 日誌和監控 =====
loguru>=0.6.0               # 進階日誌框架
rich>=12.0.0                # 美化終端輸出
click>=8.1.0                # 命令行工具

# ===== 資料分析（可選） =====
pandas>=1.5.0               # 資料分析
matplotlib>=3.5.0           # 圖表繪製
seaborn>=0.11.0             # 統計圖表

# ===== 網路和 API 測試 =====
httpx>=0.23.0               # HTTP 客戶端
responses>=0.21.0           # Mock HTTP 響應

# ===== 類型提示增強 =====
typing-extensions>=4.4.0
types-requests>=2.28.0
types-PyMySQL>=1.0.0

# ===== 打包和分發 =====
setuptools>=65.0.0
build>=0.8.0
twine>=4.0.0