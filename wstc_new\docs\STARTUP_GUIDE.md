# WSTC 系統啟動指南

## 啟動選項

### 1. 完整系統啟動（需要硬件）
```bash
python main.py
```
- 需要實際的串口設備
- 需要 MySQL 資料庫
- 適用於生產環境

### 2. 簡化系統啟動（推薦用於開發/測試）
```bash
python main_simple.py
```
- 使用線程而非多進程
- 解決 Windows 權限問題
- 更好的錯誤處理

### 3. UI 優先啟動流程測試
```bash
python test_startup_flow.py
```
- 模擬完整啟動流程
- 測試 UI 狀態顯示
- 無需硬件設備

### 4. PyQt5 功能測試
```bash
python test_qt5.py
```
- 驗證 PyQt5 安裝
- 測試基本 UI 功能

## 問題排除

### 1. 多進程權限錯誤
**錯誤**: `PermissionError: [WinError 5] 存取被拒`
**解決**: 使用 `main_simple.py` 而非 `main.py`

### 2. Pickle 序列化錯誤
**錯誤**: `cannot pickle 'weakref' object`
**解決**: 已在 `main_simple.py` 中修復，避免傳遞複雜對象

### 3. Qt 線程錯誤
**錯誤**: `Cannot create children for a parent that is in a different thread`
**解決**: 已修復，使用信號機制進行線程安全的 UI 更新

### 4. 串口連接失敗
**錯誤**: `could not open port 'COM1': FileNotFoundError`
**解決**: 正常現象，沒有實際硬件時會出現此錯誤

### 5. 資料庫連接失敗
**錯誤**: `Can't connect to MySQL server`
**解決**: 正常現象，沒有 MySQL 服務時會出現此錯誤

## 系統狀態說明

### 正常啟動流程
```
檢查系統配置...
資料庫: localhost:WSTC
串口配置: 3 個設備
============================================================
高效能實時感測器系統啟動中... (簡化版)
============================================================
正在啟動 UI 界面...
✓ UI 界面啟動中...
啟動感測器管理器線程...
感測器管理器啟動中...
[串口連接錯誤 - 正常，無硬件]
感測器管理器已啟動
啟動資料庫記錄器線程...
GUI 初始化完成 - 螢幕解析度: 1920x1080, 縮放比例: 1.00
即時數據處理已啟動
系統狀態監控已啟動
UI 界面已準備完成
UI 系統啟動中...
```

### UI 界面狀態顯示
在 UI 的訊息欄中會看到：
```
[14:30:15] UI 系統已啟動
[14:30:15] 正在啟動其他系統組件...
[14:30:17] ✓ 感測器管理器已啟動
[14:30:17]   - 扭力感測器連接中...
[14:30:17]   - RFID 讀取器連接中...
[14:30:17]   - 電壓感測器連接中...
[14:30:18] ✓ 資料庫記錄器已啟動
[14:30:18]   - 資料庫連接已建立
[14:30:18]   - 數據記錄服務已就緒
==================================================
[14:30:19] ✓ 系統啟動完成！
[14:30:19] 響應時間：1-3ms (Qt5 優化版本)
[14:30:19] 系統已準備就緒，可以開始測試
==================================================
```

## 開發建議

### 1. 開發環境
- 使用 `main_simple.py` 進行開發
- 使用 `test_startup_flow.py` 測試 UI 流程
- 使用 `test_qt5.py` 驗證 Qt5 功能

### 2. 生產環境
- 確保所有硬件設備已連接
- 確保 MySQL 服務正在運行
- 使用 `main.py` 啟動完整系統

### 3. 調試技巧
- 觀察控制台輸出了解組件狀態
- 觀察 UI 訊息欄了解啟動進度
- 檢查配置文件確保設置正確

## 配置文件

### 標準配置
- `config.ini` - 生產環境配置
- `config_demo.ini` - 演示模式配置

### 配置說明
- `demo_mode = true` - 啟用演示模式
- `simulate_sensors = true` - 模擬感測器數據
- `simulate_database = true` - 模擬資料庫操作

## 性能指標

### 啟動時間
- UI 顯示: < 2 秒
- 組件啟動: < 5 秒
- 系統就緒: < 10 秒

### 響應時間
- 數據處理: 1-3ms
- UI 更新: < 50ms
- 狀態檢查: 500ms

## 注意事項

1. **Windows 系統**: 建議使用 `main_simple.py`
2. **Linux 系統**: 可以使用 `main.py` 或 `main_simple.py`
3. **開發測試**: 優先使用測試腳本
4. **生產部署**: 確保所有依賴已安裝

---

**更新日期**: 2025-08-01
**版本**: 2.0 (Qt5 + 線程優化版本)
**狀態**: ✅ 已測試並可用
