import pymysql
import time
import datetime
import json
import traceback

remotehost = 'localhost'
local_db = 'WSTC'
box_id = '3'
       
CONFIG = []
db=pymysql.connect(host=remotehost,port=3306,user='power',passwd='1q2w3e4r',db=local_db)
cursor=db.cursor()
sql = "SELECT _KEY,_VALUE FROM _SYS"
cursor.execute(sql)
CONFIG= cursor.fetchall()
_CONFIG = {}
for con in CONFIG:
            _CONFIG[con[0]] = con[1]
cloundhost = _CONFIG['HOST_RDB']
PORT = int(_CONFIG['PORT_RDB'])
USER = _CONFIG['USER_RDB']
PASSWD = _CONFIG['PASSWD_RDB']
DB = _CONFIG['BACKUP_DB']
#TABLE=_CONFIG['TABLE1_NAME']
db.close()

while(True):
    try:
        remote_db=pymysql.connect(host=cloundhost,port=PORT,user=USER,passwd=PASSWD,db=DB)
        remote_cursor=remote_db.cursor()
    
        remote_cursor.execute("select FLAG from SSID_TABLE where NAME=%s and BOX_ID=%s",('MAPPING',box_id))
        change_table=remote_cursor.fetchall()
        remote_cursor.execute("select FLAG from SSID_TABLE where NAME=%s and BOX_ID=%s",('TEST_MEMBER',box_id))
        change_tester = remote_cursor.fetchall()
        #print(change_tester[0][0])
    
        if change_table[0][0]=='1':
            
            sql_get="SELECT BARCODE,SITE,PLANT,CUSTOMER,MACHINE_MODEL,STATION,POWER_MIN,POWER_MAX FROM MAPPING"
            sql_put="insert into INFO(BARCODE,SITE,PLANT,CUSTOMER,MACHINE_MODEL,STATION,POWER_MIN,POWER_MAX) value(%s,%s,%s,%s,%s,%s,%s,%s)"
            remote_cursor.execute(sql_get)
            database = remote_cursor.fetchall()
            print(database)
            
            locl_db=pymysql.connect(host=remotehost,port=3306,user='power',passwd='1q2w3e4r',db=local_db)
            locl_cursor=locl_db.cursor()
            sql="TRUNCATE TABLE INFO"
            locl_cursor.execute(sql)
            locl_db.commit()

            locl_cursor.executemany(sql_put,list(database))
            locl_db.commit()
            locl_cursor.close()
            locl_db.close()
            
            remote_cursor.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE NAME=%s and BOX_ID=%s',('0','MAPPING',box_id))
            remote_db.commit()
            print("["+datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')+"] [SOP] [OK]")
            #print("update ok")
        print("SOP not updated")
        
        if change_tester[0][0] == '1':
            sql_tester_get = "SELECT DEPARTMENT,JOB_NUMBER,NAME,RFID_NUMBER,PLANT,FUNCTION FROM TEST_MEMBER"
            remote_cursor.execute(sql_tester_get)
            database_tester = remote_cursor.fetchall()

            locl_db = pymysql.connect(host=remotehost, port=3306, user='power', passwd='1q2w3e4r', db=local_db)
            locl_cursor = locl_db.cursor()
            locl_cursor.execute("TRUNCATE TABLE TEST_MEMBER")
            locl_db.commit()
            sql_put_tester = "insert into TEST_MEMBER(DEPARTMENT,JOB_NUMBER,NAME,RFID_NUMBER,PLANT,FUNCTION) value(%s,%s,%s,%s,%s,%s)"
            locl_cursor.executemany(sql_put_tester, list(database_tester))
            locl_db.commit()
            locl_cursor.close()
            locl_db.close()
#             
#             sql_tester_get="SELECT DEPARTMENT,JOB_NUMBER,NAME,RFID_NUMBER,PLANT,FUNCTION FROM TEST_MEMBER"
#             remote_cursor.execute(sql_tester_get)
#             database_tester=remote_cursor.fetchall()
#             print(list(database_tester))
#             locl_db=pymysql.connect(host=remotehost,port=3306,user='power',passwd='1q2w3e4r',db=local_db)
#             locl_cursor=locl_db.cursor()
#             locl_cursor.execute("TRUNCATE TABLE TEST_MEMBER")
#             locl_db.commit()
#             sql_put_tester = "INSERT INTO TEST_MEMBER(DEPARTMENT,JOB_NUMBER,NAME,RFID_NUMBER,PLANT,FUNCTION) value(%s,%s,%s,%s,%$,%$)"
#             locl_cursor.executemany(sql_put_tester,list(database_tester))
#             locl_db.commit()
#             locl_cursor.close()
#             locl_db.close()
#             
            remote_cursor.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE NAME=%s and BOX_ID=%s',('0','TEST_MEMBER',box_id))
            remote_db.commit()
            print("["+datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')+"] [TEST_MEMBER] [OK]")
        print("TEST_MEMBER not updated")
        
        remote_cursor.close()
        remote_db.close()
                 
    except:
        print("["+datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')+"] [RDB_UP] [FAIL]",traceback.format_exc())  
    time.sleep(2)
    



    



