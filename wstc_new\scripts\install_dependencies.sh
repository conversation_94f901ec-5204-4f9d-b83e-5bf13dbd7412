#!/bin/bash

# 高效能實時感測器系統 - 依賴安裝腳本
# 適用於樹莓派和 Ubuntu/Debian 系統

echo "=========================================="
echo "安裝高效能實時感測器系統依賴"
echo "=========================================="

# 檢測作業系統
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "檢測到 Linux 系統"
    
    # 檢測發行版
    if command -v apt-get &> /dev/null; then
        DISTRO="debian"
        echo "檢測到 Debian/Ubuntu 系統"
    elif command -v yum &> /dev/null; then
        DISTRO="rhel"
        echo "檢測到 RHEL/CentOS 系統"
    elif command -v dnf &> /dev/null; then
        DISTRO="fedora"
        echo "檢測到 Fedora 系統"
    else
        DISTRO="unknown"
        echo "未知的 Linux 發行版"
    fi
    
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    echo "檢測到 Windows 系統"
    DISTRO="windows"
else
    echo "檢測到其他系統: $OSTYPE"
    DISTRO="other"
fi

# 更新套件管理器
echo "更新套件管理器..."
if [[ "$DISTRO" == "debian" ]]; then
    sudo apt-get update
elif [[ "$DISTRO" == "rhel" ]]; then
    sudo yum update -y
elif [[ "$DISTRO" == "fedora" ]]; then
    sudo dnf update -y
fi

# 安裝系統依賴
echo "安裝系統依賴..."
if [[ "$DISTRO" == "debian" ]]; then
    # Debian/Ubuntu/樹莓派
    sudo apt-get install -y \
        python3 \
        python3-pip \
        python3-dev \
        python3-pyqt5 \
        python3-venv \
        build-essential \
        libffi-dev \
        libssl-dev \
        mysql-client \
        git
        
    # 樹莓派特定依賴
    if [[ $(uname -m) == "arm"* ]] || [[ $(uname -m) == "aarch64" ]]; then
        echo "安裝樹莓派特定依賴..."
        sudo apt-get install -y \
            python3-evdev \
            libraspberrypi-dev \
            raspi-gpio
    fi
    
elif [[ "$DISTRO" == "rhel" ]]; then
    # RHEL/CentOS
    sudo yum install -y \
        python3 \
        python3-pip \
        python3-devel \
        python3-qt5 \
        gcc \
        openssl-devel \
        libffi-devel \
        mysql \
        git
        
elif [[ "$DISTRO" == "fedora" ]]; then
    # Fedora
    sudo dnf install -y \
        python3 \
        python3-pip \
        python3-devel \
        python3-qt5 \
        gcc \
        openssl-devel \
        libffi-devel \
        mysql \
        git
fi

# 升級 pip
echo "升級 pip..."
python3 -m pip install --upgrade pip

# 建立虛擬環境（可選）
if [ "$1" = "venv" ]; then
    echo "建立虛擬環境..."
    python3 -m venv venv
    source venv/bin/activate
    echo "虛擬環境已啟動"
fi

# 安裝 Python 依賴
echo "安裝 Python 依賴..."
pip3 install -r requirements.txt

# 驗證安裝
echo "=========================================="
echo "驗證安裝..."
echo "=========================================="

# 檢查 Python 版本
python3 --version

# 檢查關鍵模組
echo "檢查關鍵模組..."
python3 -c "import pymysql; print('✓ pymysql 已安裝')" 2>/dev/null || echo "✗ pymysql 安裝失敗"
python3 -c "import serial; print('✓ pyserial 已安裝')" 2>/dev/null || echo "✗ pyserial 安裝失敗"
python3 -c "import requests; print('✓ requests 已安裝')" 2>/dev/null || echo "✗ requests 安裝失敗"
python3 -c "import PyQt5; print('✓ PyQt5 已安裝')" 2>/dev/null || echo "✗ PyQt5 安裝失敗"

# 檢查平台特定模組
if [[ $(uname -m) == "arm"* ]] || [[ $(uname -m) == "aarch64" ]] || [[ "$OSTYPE" == "linux-gnu"* ]]; then
    python3 -c "import evdev; print('✓ evdev 已安裝（Linux）')" 2>/dev/null || echo "⚠ evdev 未安裝（Linux專用）"
fi

python3 -c "import pynput; print('✓ pynput 已安裝')" 2>/dev/null || echo "✗ pynput 安裝失敗"

# 檢查串口權限（Linux）
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "檢查串口權限..."
    if groups $USER | grep -q "dialout"; then
        echo "✓ 使用者已在 dialout 群組"
    else
        echo "⚠ 需要將使用者加入 dialout 群組："
        echo "   sudo usermod -a -G dialout $USER"
        echo "   然後重新登入"
    fi
fi

# 檢查 MySQL 連接
echo "檢查 MySQL 服務..."
if command -v mysql &> /dev/null; then
    if systemctl is-active --quiet mysql || systemctl is-active --quiet mariadb; then
        echo "✓ MySQL/MariaDB 服務正在運行"
    else
        echo "⚠ MySQL/MariaDB 服務未運行"
        echo "   啟動服務: sudo systemctl start mysql"
    fi
else
    echo "⚠ MySQL 客戶端未安裝"
fi

echo "=========================================="
echo "依賴安裝完成！"
echo "=========================================="

# 提供下一步指示
echo "下一步："
echo "1. 編輯 config.ini 設置資料庫和串口"
echo "2. 確保 MySQL 服務正在運行"
echo "3. 如果是樹莓派，確保串口已啟用"
echo "4. 執行: python3 main.py"
echo ""
echo "或使用一鍵啟動:"
echo "./start_system.sh"