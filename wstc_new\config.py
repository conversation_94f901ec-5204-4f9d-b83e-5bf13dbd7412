"""
系統配置文件
"""
import configparser
import os

class Config:
    """配置管理類"""
    
    def __init__(self, config_file='configs/config.ini'):
        self.config = configparser.ConfigParser()
        self.config_file = config_file
        self.load_config()
    
    def load_config(self):
        """載入配置文件"""
        # 確保 configs 資料夾存在
        config_dir = os.path.dirname(self.config_file)
        if config_dir and not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)

        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self.create_default_config()
    
    def create_default_config(self):
        """建立預設配置"""
        # 資料庫配置
        self.config['LocalDatabase'] = {
            'local_host': 'localhost',
            'local_user': 'power',
            'local_password': '1q2w3e4r',
            'local_database': 'WSTC'
        }
        
        # 串口配置（樹莓派）
        self.config['SerialPorts_RPi'] = {
            'torque_port': '/dev/ttyAMA0',
            'voltage_port': '/dev/ttyAMA4',
            'rfid_port': '/dev/ttyAMA2',
            'torque_baudrate': '115200',
            'voltage_baudrate': '9600',
            'rfid_baudrate': '9600'
        }
        
        # 串口配置（Windows）
        self.config['SerialPorts_Windows'] = {
            'torque_port': 'COM1',
            'voltage_port': 'COM4',
            'rfid_port': 'COM2',
            'torque_baudrate': '115200',
            'voltage_baudrate': '9600',
            'rfid_baudrate': '9600'
        }
        
        # 系統設置
        self.config['System'] = {
            'platform': 'auto',  # auto, rpi, windows
            'ui_update_interval': '0.05',  # 50ms
            'sensor_poll_interval': '0.1',  # 100ms
            'heartbeat_interval': '1.0',   # 1秒
            'queue_max_size': '100',
            'debug_mode': 'true'
        }
        
        # 扭力設置
        self.config['TorqueSettings'] = {
            'data_filter_enabled': 'true',
            'min_torque_change': '0.1',
            'max_torque_value': '1000',
            'torque_timeout': '5.0'
        }
        
        # 網路設置
        self.config['Network'] = {
            'webservice_url': 'http://10.55.241.33:138/Tester.WebserviceNewVersion/WebService.asmx',
            'wifi_check_interval': '30',
            'connection_timeout': '10'
        }

        # 設備配置
        self.config['Device'] = {
            'station_id': '1',
            'station_name': '工站1',
            'box_id': '1'
        }
        
        self.save_config()
    
    def save_config(self):
        """儲存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def get(self, section: str, key: str, fallback=None):
        """獲取配置值"""
        return self.config.get(section, key, fallback=fallback)
    
    def getint(self, section: str, key: str, fallback=0):
        """獲取整數配置值"""
        return self.config.getint(section, key, fallback=fallback)
    
    def getfloat(self, section: str, key: str, fallback=0.0):
        """獲取浮點配置值"""
        return self.config.getfloat(section, key, fallback=fallback)
    
    def getboolean(self, section: str, key: str, fallback=False):
        """獲取布林配置值"""
        return self.config.getboolean(section, key, fallback=fallback)
    
    def get_db_config(self):
        """獲取資料庫配置"""
        return {
            'host': self.get('LocalDatabase', 'local_host'),
            'user': self.get('LocalDatabase', 'local_user'),
            'password': self.get('LocalDatabase', 'local_password'),
            'database': self.get('LocalDatabase', 'local_database'),
            'port': 3306,
            'charset': 'utf8'
        }
    
    def get_serial_config(self, platform='auto'):
        """獲取串口配置"""
        import platform as plt
        
        if platform == 'auto':
            platform = 'RPi' if 'arm' in plt.machine().lower() else 'Windows'
        
        section = f'SerialPorts_{platform}'
        
        return {
            'torque': {
                'port': self.get(section, 'torque_port'),
                'baudrate': self.getint(section, 'torque_baudrate'),
                'timeout': 0.5
            },
            'voltage': {
                'port': self.get(section, 'voltage_port'),
                'baudrate': self.getint(section, 'voltage_baudrate'),
                'timeout': 1.0
            },
            'rfid': {
                'port': self.get(section, 'rfid_port'),
                'baudrate': self.getint(section, 'rfid_baudrate'),
                'timeout': 1.0
            }
        }

    def get_device_config(self):
        """獲取設備配置"""
        return {
            'station_id': self.config.getint('Device', 'station_id'),
            'station_name': self.config.get('Device', 'station_name'),
            'box_id': self.config.get('Device', 'box_id')
        }

    def get_network_config(self):
        """獲取網路配置"""
        return {
            'webservice_url': self.config.get('Network', 'webservice_url'),
            'wifi_check_interval': self.config.getint('Network', 'wifi_check_interval'),
            'connection_timeout': self.config.getint('Network', 'connection_timeout')
        }

# 全域配置實例
config = Config()