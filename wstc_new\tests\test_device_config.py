#!/usr/bin/env python3
"""
測試設備配置功能
"""
import sys
import os

# 添加父目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config_import():
    """測試配置模組導入"""
    try:
        from config import config
        print("✓ 配置模組導入成功")
        return True
    except ImportError as e:
        print(f"✗ 配置模組導入失敗: {e}")
        return False

def test_device_config_reading():
    """測試設備配置讀取"""
    try:
        from config import config
        
        device_config = config.get_device_config()
        
        print("✓ 設備配置讀取成功")
        print(f"  - 工站ID: {device_config['station_id']}")
        print(f"  - 工站名稱: {device_config['station_name']}")
        print(f"  - Box ID: {device_config['box_id']}")
        
        # 驗證數據類型
        if isinstance(device_config['station_id'], int):
            print("✓ 工站ID類型正確 (int)")
        else:
            print(f"⚠️ 工站ID類型錯誤: {type(device_config['station_id'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ 設備配置讀取失敗: {e}")
        return False

def test_database_sync_config():
    """測試資料庫同步器配置"""
    try:
        from database_sync import DatabaseSynchronizer
        
        synchronizer = DatabaseSynchronizer()
        
        print("✓ 資料庫同步器配置讀取成功")
        print(f"  - Box ID: {synchronizer.box_id}")
        
        return True
        
    except Exception as e:
        print(f"✗ 資料庫同步器配置測試失敗: {e}")
        return False

def test_ui_station_display():
    """測試 UI 工站顯示"""
    try:
        # 模擬 UI 工站顯示邏輯
        from config import config
        
        device_config = config.get_device_config()
        station_display = f"#{device_config['station_id']}"
        
        print("✓ UI 工站顯示測試成功")
        print(f"  - 顯示文字: {station_display}")
        
        return True
        
    except Exception as e:
        print(f"✗ UI 工站顯示測試失敗: {e}")
        return False

def test_config_manager():
    """測試配置管理器"""
    try:
        # 添加 tools 目錄到路徑
        tools_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'tools')
        sys.path.insert(0, tools_path)
        from config_manager import ConfigManager
        
        manager = ConfigManager()
        
        # 測試列出配置
        configs = manager.list_available_configs()
        print("✓ 配置管理器測試成功")
        print(f"  - 找到 {len(configs)} 個工站配置")
        
        for config_info in configs:
            print(f"    工站{config_info['station']}: {config_info['name']}")
        
        # 測試獲取當前工站
        current_station = manager.get_current_station()
        if current_station:
            print(f"  - 當前工站: #{current_station}")
        else:
            print("  - 當前工站: 未知")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器測試失敗: {e}")
        return False

def test_multiple_station_configs():
    """測試多工站配置"""
    try:
        import configparser
        from pathlib import Path
        
        config_dir = Path("configs")
        station_configs = list(config_dir.glob("config_station*.ini"))
        
        print(f"✓ 找到 {len(station_configs)} 個工站配置文件")
        
        for config_file in station_configs:
            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')
            
            if 'Device' in config:
                station_id = config.get('Device', 'station_id')
                station_name = config.get('Device', 'station_name')
                print(f"  - {config_file.name}: 工站{station_id} ({station_name})")
            else:
                print(f"  - {config_file.name}: 無設備配置")
        
        return True
        
    except Exception as e:
        print(f"✗ 多工站配置測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("設備配置功能測試")
    print("=" * 60)
    
    tests = [
        ("配置模組導入", test_config_import),
        ("設備配置讀取", test_device_config_reading),
        ("資料庫同步器配置", test_database_sync_config),
        ("UI 工站顯示", test_ui_station_display),
        ("配置管理器", test_config_manager),
        ("多工站配置", test_multiple_station_configs),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} 發生異常: {e}")
    
    print("\n" + "=" * 60)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("✓ 所有測試通過！設備配置功能已準備就緒")
        return True
    else:
        print("⚠️ 部分測試未通過")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
