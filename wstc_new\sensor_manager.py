"""
感測器管理器 - 高效能版本
使用事件驅動和多線程，即時響應所有感測器
"""
import threading
import time
import serial
import binascii
import platform
from typing import Optional

# 樹莓派專用
try:
    import evdev
    HAS_EVDEV = True
except ImportError:
    HAS_EVDEV = False

# 跨平台鍵盤監聽
try:
    from pynput import keyboard
    HAS_PYNPUT = True
except ImportError:
    HAS_PYNPUT = False

from shared_data import get_data_manager, DeviceType, SensorData
from config import config

class KeyboardInputHandler:
    """跨平台鍵盤輸入處理器"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.input_buffer = ""
        self.use_evdev = HAS_EVDEV and platform.machine().startswith('arm')
        self.listener = None
        
        # evdev 鍵碼映射表
        self.keymap = {
            'KEY_0': '0', 'KEY_1': '1', 'KEY_2': '2', 'KEY_3': '3',
            'KEY_4': '4', 'KEY_5': '5', 'KEY_6': '6', 'KEY_7': '7',
            'KEY_8': '8', 'KEY_9': '9', 'KEY_A': 'A', 'KEY_B': 'B',
            'KEY_C': 'C', 'KEY_D': 'D', 'KEY_E': 'E', 'KEY_F': 'F',
            'KEY_G': 'G', 'KEY_H': 'H', 'KEY_I': 'I', 'KEY_J': 'J',
            'KEY_K': 'K', 'KEY_L': 'L', 'KEY_M': 'M', 'KEY_N': 'N',
            'KEY_O': 'O', 'KEY_P': 'P', 'KEY_Q': 'Q', 'KEY_R': 'R',
            'KEY_S': 'S', 'KEY_T': 'T', 'KEY_U': 'U', 'KEY_V': 'V',
            'KEY_W': 'W', 'KEY_X': 'X', 'KEY_Y': 'Y', 'KEY_Z': 'Z',
            'KEY_SPACE': ' ', 'KEY_MINUS': '-', 'KEY_DOT': '.',
            'KEY_KP0': '0', 'KEY_KP1': '1', 'KEY_KP2': '2', 'KEY_KP3': '3',
            'KEY_KP4': '4', 'KEY_KP5': '5', 'KEY_KP6': '6', 'KEY_KP7': '7',
            'KEY_KP8': '8', 'KEY_KP9': '9', 'KEY_KPDOT': '.'
        }
    
    def start(self):
        """啟動鍵盤監聽"""
        if self.use_evdev:
            self._start_evdev()
        elif HAS_PYNPUT:
            self._start_pynput()
        else:
            print("警告：無可用的鍵盤輸入庫")
    
    def _start_evdev(self):
        """使用 evdev 監聽（樹莓派）"""
        def evdev_worker():
            try:
                devices = [evdev.InputDevice(path) for path in evdev.list_devices()]
                print(f"發現設備: {len(devices)} 個")
                
                for device in devices:
                    thread = threading.Thread(
                        target=self._handle_evdev_device, 
                        args=(device,), 
                        daemon=True
                    )
                    thread.start()
                    
            except Exception as e:
                print(f"evdev 初始化失敗: {e}")
        
        threading.Thread(target=evdev_worker, daemon=True).start()
    
    def _handle_evdev_device(self, device):
        """處理 evdev 設備事件"""
        buf = ''
        try:
            for event in device.read_loop():
                if event.type == evdev.ecodes.EV_KEY and event.value == 1:
                    key_event = evdev.events.KeyEvent(event)
                    
                    if key_event.scancode == evdev.ecodes.KEY_ENTER:
                        if buf:
                            self._process_input(buf, len(device.name))
                            buf = ''
                    else:
                        char = self.keymap.get(key_event.keycode, '')
                        buf += char
                        
        except Exception as e:
            print(f"設備 {device.name} 錯誤: {e}")
    
    def _start_pynput(self):
        """使用 pynput 監聽（跨平台）"""
        def on_press(key):
            try:
                if hasattr(key, 'char') and key.char is not None:
                    self.input_buffer += key.char
                elif key == keyboard.Key.enter:
                    if self.input_buffer:
                        self._process_input(self.input_buffer, 13)  # 假設設備名長度
                        self.input_buffer = ""
                elif key == keyboard.Key.backspace:
                    if self.input_buffer:
                        self.input_buffer = self.input_buffer[:-1]
            except AttributeError:
                pass
        
        self.listener = keyboard.Listener(on_press=on_press)
        self.listener.start()
    
    def _process_input(self, buffer: str, device_name_length: int):
        """處理輸入數據"""
        try:
            buf_len = len(buffer)
            
            # 根據設備名長度和數據長度判斷設備類型
            if device_name_length == 13:  # 特定設備
                if buf_len == 4:
                    # 螺絲刀條碼
                    self.data_manager.send_sensor_data(DeviceType.SCREWDRIVER, buffer)
                    print(f"螺絲刀條碼: {buffer}")
                elif buf_len == 12:
                    # 工單號
                    self.data_manager.send_sensor_data(DeviceType.WORK, buffer)
                    print(f"工單號: {buffer}")
                else:
                    # 一般掃碼
                    self.data_manager.send_sensor_data(DeviceType.SCAN, buffer)
                    print(f"掃碼: {buffer}")
            else:
                # 預設為掃碼槍
                self.data_manager.send_sensor_data(DeviceType.SCAN, buffer)
                print(f"掃碼: {buffer}")
                
        except Exception as e:
            print(f"處理輸入錯誤: {e}")
    
    def stop(self):
        """停止監聽"""
        if self.listener:
            self.listener.stop()

class SerialSensorManager:
    """串口感測器管理器"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.serial_config = config.get_serial_config()
        self.running = False
        self.threads = []
    
    def start(self):
        """啟動所有串口感測器"""
        self.running = True
        
        # 啟動扭力感測器
        torque_thread = threading.Thread(
            target=self._torque_sensor_worker, 
            daemon=True
        )
        torque_thread.start()
        self.threads.append(torque_thread)
        
        # 啟動電壓感測器
        voltage_thread = threading.Thread(
            target=self._voltage_sensor_worker, 
            daemon=True
        )
        voltage_thread.start()
        self.threads.append(voltage_thread)
        
        # 啟動 RFID 感測器
        rfid_thread = threading.Thread(
            target=self._rfid_sensor_worker, 
            daemon=True
        )
        rfid_thread.start()
        self.threads.append(rfid_thread)
        
        print("所有串口感測器已啟動")
    
    def _torque_sensor_worker(self):
        """扭力感測器工作線程"""
        config_data = self.serial_config['torque']
        
        try:
            ser = serial.Serial(
                port=config_data['port'],
                baudrate=config_data['baudrate'],
                timeout=config_data['timeout']
            )
            print(f"扭力感測器已連接: {config_data['port']}")
            
            while self.running:
                try:
                    ser.reset_input_buffer()
                    ser.reset_output_buffer()
                    data = ser.readline()
                    
                    if data:
                        hex_data = binascii.b2a_hex(data)
                        result = str(hex_data)
                        
                        if len(result) >= 9 and result[-9:-1] == 'cffcccff':
                            power_value = self._parse_torque_data(data)
                            if power_value is not None:
                                self.data_manager.send_sensor_data(
                                    DeviceType.POWER, 
                                    str(power_value)
                                )
                                print(f"扭力值: {power_value}")
                    
                    time.sleep(0.1)  # 100ms 輪詢間隔
                    
                except Exception as e:
                    print(f"扭力感測器數據錯誤: {e}")
                    time.sleep(1)
                    
        except Exception as e:
            print(f"扭力感測器連接失敗: {e}")
    
    def _parse_torque_data(self, data: bytes) -> Optional[float]:
        """解析扭力數據"""
        try:
            hex_data = data.hex().upper()
            formatted_data = ' '.join([hex_data[i:i + 2] for i in range(0, len(hex_data), 2)])
            tdata = formatted_data.replace("\n", " ").split()
            
            segment_length = 12
            variable_values = []
            
            for i in range(3, len(tdata), segment_length):
                if i + 4 < len(tdata):
                    variable_value = ''.join(tdata[i:i+5])
                    variable_values.append(int(variable_value, 16) / 10)
            
            return max(variable_values) if variable_values else None
            
        except Exception as e:
            print(f"扭力數據解析錯誤: {e}")
            return None
    
    def _voltage_sensor_worker(self):
        """電壓感測器工作線程"""
        config_data = self.serial_config['voltage']
        
        try:
            ser = serial.Serial(
                port=config_data['port'],
                baudrate=config_data['baudrate'],
                timeout=config_data['timeout']
            )
            print(f"電壓感測器已連接: {config_data['port']}")
            
            v_command = bytes.fromhex('03 04 00 03 00 01 C0 28')
            
            while self.running:
                try:
                    ser.write(v_command)
                    time.sleep(1)
                    data = ser.readall()
                    
                    if data:
                        voltage_value = self._parse_voltage_data(data)
                        if voltage_value is not None:
                            self.data_manager.send_sensor_data(
                                DeviceType.VOLTAGE, 
                                str(voltage_value)
                            )
                            print(f"電壓值: {voltage_value}%")
                    
                except Exception as e:
                    print(f"電壓感測器數據錯誤: {e}")
                    time.sleep(1)
                    
        except Exception as e:
            print(f"電壓感測器連接失敗: {e}")
    
    def _parse_voltage_data(self, data: bytes) -> Optional[float]:
        """解析電壓數據"""
        try:
            hex_data = binascii.hexlify(data)
            hex_str = str(hex_data, encoding="utf-8")
            
            if len(hex_str) >= 10:
                raw_value = int(hex_str[8:10], 16)
                voltage = (((raw_value - 0x33) * 0.064) + 6.14)
                percentage = (voltage - 6.2) * 45.87
                return round(percentage, 2)
            
            return None
            
        except Exception as e:
            print(f"電壓數據解析錯誤: {e}")
            return None
    
    def _rfid_sensor_worker(self):
        """RFID 感測器工作線程"""
        config_data = self.serial_config['rfid']
        
        try:
            ser = serial.Serial(
                port=config_data['port'],
                baudrate=config_data['baudrate'],
                timeout=config_data['timeout']
            )
            print(f"RFID 感測器已連接: {config_data['port']}")
            
            rfid_command = bytes.fromhex('55 00 02 05 00 00 05')
            
            while self.running:
                try:
                    ser.write(rfid_command)
                    data = ser.readline()
                    
                    if data:
                        rfid_value = str(data, 'utf-8').replace('\r\n', '').replace(' ', '')
                        
                        if rfid_value and rfid_value != '0000000000':
                            self.data_manager.send_sensor_data(
                                DeviceType.RFID, 
                                rfid_value
                            )
                            print(f"RFID: {rfid_value}")
                    
                    time.sleep(0.5)  # 500ms 輪詢間隔
                    
                except Exception as e:
                    print(f"RFID 感測器數據錯誤: {e}")
                    time.sleep(1)
                    
        except Exception as e:
            print(f"RFID 感測器連接失敗: {e}")
    
    def stop(self):
        """停止所有感測器"""
        self.running = False

class SensorManager:
    """主感測器管理器"""
    
    def __init__(self):
        self.data_manager = get_data_manager()
        self.keyboard_handler = KeyboardInputHandler(self.data_manager)
        self.serial_manager = SerialSensorManager(self.data_manager)
        self.running = False
    
    def _control_signal_worker(self):
        """控制信號監聽線程"""
        while self.running:
            try:
                control_signal = self.data_manager.get_control_signal(timeout=1.0)
                if control_signal:
                    signal_type = control_signal.get('signal')
                    if signal_type == 'SHUTDOWN':
                        print("感測器管理器收到關閉信號")
                        self.stop()
                        break
            except:
                pass

    def start(self):
        """啟動感測器管理器"""
        print("感測器管理器啟動中...")
        self.running = True
        
        # 更新系統狀態
        self.data_manager.system_status['sensor_active'] = True
        
        # 啟動各個組件
        self.keyboard_handler.start()
        self.serial_manager.start()
        
        # 啟動心跳線程
        heartbeat_thread = threading.Thread(target=self._heartbeat_worker, daemon=True)
        heartbeat_thread.start()
        
        # 啟動控制信號監聽線程
        control_thread = threading.Thread(target=self._control_signal_worker, daemon=True)
        control_thread.start()
        
        print("感測器管理器已啟動")

    def _heartbeat_worker(self):
        """心跳工作線程"""
        while self.running:
            self.data_manager.update_heartbeat('sensor')
            time.sleep(config.getfloat('System', 'heartbeat_interval', 1.0))

    def stop(self):
        """停止所有感測器"""
        print("正在停止感測器管理器...")
        self.running = False
        
        self.keyboard_handler.stop()
        self.serial_manager.stop()
        
        self.data_manager.system_status['sensor_active'] = False
        print("感測器管理器已停止")

if __name__ == "__main__":
    manager = SensorManager()
    
    try:
        manager.start()
        
        # 保持運行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到停止信號...")
        manager.stop()
