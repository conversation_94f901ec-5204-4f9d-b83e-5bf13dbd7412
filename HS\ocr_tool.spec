# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

added_files = [
    ('models', 'models'),
    ('requirements.txt', '.'),
]

hidden_imports = [
    'easyocr',
    'torch',
    'torchvision',
    'PIL',
    'cv2',
    'numpy',
    'requests',
    'pygame',
    'tkinter',
    'threading',
    'datetime',
    're',
    'warnings',
    'torch.jit',
    'torch.nn',
    'torch.utils.data',
    'torchvision.transforms',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
]

a = Analysis(
    ['main.py'],
    pathex=['D:\\HS'],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ocr_tool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None
)
