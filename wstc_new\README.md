# WSTC 高效能測試系統

## 快速開始

### Windows 用戶
```cmd
# 批次檔啟動
.\start.bat

# PowerShell 啟動（推薦）
.\start.ps1
```

### Linux 用戶
```bash
./start.sh
```

### 手動啟動
```bash
# 推薦：簡化模式（線程版本）
python main_simple.py

# 完整模式（多進程版本）
python main.py
```

## 系統特色

- ✅ **高效能**: 響應時間 1-3ms (相比原系統 500-1000ms)
- ✅ **現代化 UI**: 基於 PyQt5 的專業界面
- ✅ **實時監控**: 即時顯示系統啟動狀態
- ✅ **多工站支援**: 支援多台設備獨立配置和序號管理
- ✅ **資料庫同步**: 自動同步遠端資料庫資料
- ✅ **跨平台**: 支援 Windows、Linux、樹莓派
- ✅ **易於部署**: 自動化安裝腳本

## 項目結構

```
wstc_new/
├── 🚀 主要程式
│   ├── main_simple.py      # 推薦啟動程式
│   ├── main.py             # 完整版啟動程式
│   ├── main_ui.py          # Qt5 用戶界面
│   ├── database_sync.py    # 資料庫同步器
│   └── ...                 # 其他核心模組
├── 📁 tools/               # 工具程式
├── 📁 utils/               # 輔助程式
├── 📁 ui_designer/         # UI 設計器
├── 📁 configs/             # 配置文件
├── 📁 tests/               # 測試腳本
├── 📁 scripts/             # 安裝腳本
├── 📁 docs/                # 文檔說明
└── 📁 legacy/              # 舊版本備份
```

## 安裝依賴

### 自動安裝
```bash
# Windows
scripts/install_dependencies.bat

# Linux
scripts/install_dependencies.sh
```

### 手動安裝
```bash
pip install -r requirements.txt
```

## 功能測試

```bash
# PyQt5 功能測試
python tests/test_qt5.py

# 啟動流程測試
python tests/test_startup_flow.py

# 資料庫同步器測試
python tests/test_database_sync.py

# 設備配置測試
python tests/test_device_config.py
```

## 設備配置管理

### 查看當前工站
```bash
python tools/config_manager.py status
```

### 切換工站配置
```bash
python tools/config_manager.py switch 1  # 切換到工站1
```

### 創建新工站配置
```bash
python tools/config_manager.py create 5  # 創建工站5配置
```

## 配置

- **生產環境**: `configs/config.ini`
- **演示模式**: `configs/config_demo.ini`

## 文檔

詳細文檔請查看 `docs/` 資料夾：

- [Qt5 遷移說明](docs/QT5_MIGRATION_README.md)
- [啟動流程說明](docs/STARTUP_FLOW_README.md)
- [啟動指南](docs/STARTUP_GUIDE.md)
- [項目結構說明](PROJECT_STRUCTURE.md)

## 系統需求

- **Python**: 3.6+
- **作業系統**: Windows 7+, Ubuntu 18.04+, Debian 9+
- **記憶體**: 最少 512MB
- **螢幕解析度**: 建議 1024x768 以上

## 支援

如遇問題請參考：
1. [啟動指南](docs/STARTUP_GUIDE.md)
2. [故障排除](docs/QT5_MIGRATION_README.md#故障排除)
3. 運行測試腳本診斷問題

---

**版本**: 2.0 (Qt5 優化版本)  
**更新日期**: 2025-08-01  
**狀態**: ✅ 生產就緒
