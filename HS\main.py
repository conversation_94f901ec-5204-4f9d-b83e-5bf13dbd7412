import tkinter as tk
from tkinter import ttk
from tkinter import messagebox
import os
import sys
import warnings
# 過濾特定的 FutureWarning
warnings.filterwarnings('ignore', category=FutureWarning, module='torch.serialization')

# 定義模型路徑
MODEL_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'models')

# 延遲載入的模組
def import_ocr_dependencies():
    """延遲載入 OCR 相關依賴"""
    global cv2, np, pyautogui, easyocr, Image, ImageTk, torch
    import cv2
    import numpy as np
    import pyautogui
    import easyocr
    from PIL import Image, ImageTk
    import torch
    
    # 配置 PyTorch
    torch.set_grad_enabled(False)  # 禁用梯度計算
    if torch.cuda.is_available():
        torch.backends.cudnn.benchmark = True  # 啟用 cuDNN 自動調優

def download_models():
    """預下載 EasyOCR 模型"""
    try:
        if not os.path.exists(MODEL_PATH):
            os.makedirs(MODEL_PATH)
        import_ocr_dependencies()
        # 配置 reader 使用 weights_only=True
        reader = easyocr.Reader(
            ['en'],
            model_storage_directory=MODEL_PATH,
            download_enabled=True,
            verbose=False,
            gpu=torch.cuda.is_available()
        )
        return True
    except Exception as e:
        print(f"模型下載失敗: {str(e)}")
        return False

class ResultWindow:
    def __init__(self, root, result_type):
        self.window = tk.Toplevel(root)
        self.window.overrideredirect(True)
        
        # 獲取屏幕尺寸
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        
        # 設置窗口大小為屏幕的一半
        window_width = screen_width // 2
        window_height = screen_height // 2
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.window.attributes('-topmost', True)
        
        # 創建標籤
        if result_type == "PASS":
            label = tk.Label(self.window, text="PASS", font=("Arial", 120, "bold"), fg="blue", bg="white")
            self.window.configure(bg="white")
            self.window.after(1000, self.window.destroy)
        else:
            label = tk.Label(self.window, text="NG", font=("Arial", 200, "bold"), fg="red", bg="white")
            self.window.configure(bg="white")
            button = ttk.Button(self.window, text="確認", command=self.window.destroy)
            button.pack(side="bottom", pady=30)
        
        label.pack(expand=True)

class OCRApp:
    def __init__(self, root):
        self.root = root
        self.root.title("數字OCR工具")
        
        # 初始化變量
        self.reader = None
        self.current_color = "black"
        self.capturing = False
        self.start_x = None
        self.start_y = None
        self.selection_window = None
        self.overlay_window = None
        self.current_region = None
        self.scanning = False
        self.scan_thread = None
        self.last_number = "0000000000000"
        self.dependencies_loaded = False
        
        # 創建UI
        self._create_ui()
    
    def _create_ui(self):
        """創建UI元件"""
        # 使用grid布局來提高效率
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(2, weight=1)
        
        # 創建狀態框架
        self.status_frame = ttk.LabelFrame(self.root, text="狀態")
        self.status_frame.grid(row=0, column=0, padx=10, pady=5, sticky="ew")
        
        # 創建數字標籤和狀態標籤
        self.number_label = tk.Label(self.status_frame, text="當前SN: 0000000000000", 
                                   font=("Arial", 24, "bold"),
                                   fg="black")
        self.number_label.pack(padx=5, pady=5)
        
        self.status_label = ttk.Label(self.status_frame, text="準備就緒")
        self.status_label.pack(padx=5, pady=2)
        
        # 創建按鈕框架
        self.button_frame = ttk.Frame(self.root)
        self.button_frame.grid(row=1, column=0, padx=10, pady=5, sticky="ew")
        
        # 創建按鈕
        self.start_button = ttk.Button(self.button_frame, text="開始擷取", 
                                     command=self.start_capture)
        self.start_button.pack(side="left", padx=5)
        
        self.stop_button = ttk.Button(self.button_frame, text="結束擷取", 
                                    command=self.stop_selection,
                                    state="disabled")
        self.stop_button.pack(side="left", padx=5)
        
        # 創建自動掃描開關
        self.auto_scan_var = tk.BooleanVar()
        self.auto_scan_check = ttk.Checkbutton(self.button_frame, text="自動掃描", 
                                              variable=self.auto_scan_var,
                                              command=self.toggle_auto_scan,
                                              state="disabled")
        self.auto_scan_check.pack(side="left", padx=5)
        
        # 創建日誌框架
        self.log_frame = ttk.LabelFrame(self.root, text="工作狀態日誌")
        self.log_frame.grid(row=2, column=0, padx=10, pady=5, sticky="nsew")
        
        # 配置日誌框架的grid
        self.log_frame.grid_columnconfigure(0, weight=1)
        self.log_frame.grid_rowconfigure(0, weight=1)
        
        # 創建日誌文本框和滾動條
        self.log_text = tk.Text(self.log_frame, height=10, wrap=tk.WORD)
        self.log_scrollbar = ttk.Scrollbar(self.log_frame, orient="vertical", 
                                         command=self.log_text.yview)
        
        # 配置日誌文本框和滾動條
        self.log_text.grid(row=0, column=0, sticky="nsew")
        self.log_scrollbar.grid(row=0, column=1, sticky="ns")
        self.log_text.configure(yscrollcommand=self.log_scrollbar.set, state='disabled')
        
        # 初始化浮動窗口
        self.float_window = None
    
    def _load_dependencies(self):
        """延遲載入依賴"""
        if not self.dependencies_loaded:
            import_ocr_dependencies()
            import threading
            import time
            import datetime
            import re
            import requests
            self.dependencies_loaded = True
    
    def _initialize_ocr(self):
        """初始化 OCR（離線模式）"""
        if self.reader is None:
            try:
                self.status_label.config(text="正在初始化OCR...")
                self.root.update()
                
                self._load_dependencies()
                
                # 使用本地模型初始化，添加 weights_only=True
                self.reader = easyocr.Reader(
                    ['en'],
                    gpu=torch.cuda.is_available(),
                    model_storage_directory=MODEL_PATH,
                    download_enabled=False,
                    verbose=False,
                    recog_network='standard'  # 使用標準網絡以提高速度
                )
                self.status_label.config(text="準備就緒")
                return True
            except Exception as e:
                error_msg = f"OCR初始化失敗: {str(e)}"
                messagebox.showerror("錯誤", error_msg)
                self.status_label.config(text="OCR初始化失敗")
                return False
        return True

    def add_log(self, message):
        """添加日誌信息到日誌文本框和文件"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        # 添加到GUI日誌
        self.log_text.configure(state='normal')
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.log_text.configure(state='disabled')
        
        # 保存到文件
        try:
            # 使用當前日期作為文件名
            log_filename = f"log_{datetime.datetime.now().strftime('%Y%m%d')}.txt"
            with open(log_filename, 'a', encoding='utf-8') as f:
                f.write(log_message)
        except Exception as e:
            self.status_label.config(text=f"保存日誌錯誤: {str(e)}")

    def save_number_to_file(self, number):
        # 獲取當前時間戳
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 創建文件名（使用當前日期）
        filename = f"numbers_{datetime.datetime.now().strftime('%Y%m%d')}.txt"
        
        # 寫入文件
        try:
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(f"{timestamp} - 數字變化: {self.last_number} -> {number}\n")
            self.add_log(f"數字變化已記錄: {self.last_number} -> {number}")
        except Exception as e:
            error_msg = f"保存文件錯誤: {str(e)}"
            self.status_label.config(text=error_msg)
            self.add_log(error_msg)

    def extract_13_digit_number(self, text):
        # 合併所有文本並移除空格
        combined_text = ''.join(text).replace(' ', '')
        # 查找所有數字序列
        numbers = re.findall(r'\d+', combined_text)
        # 只返回13位數字
        for num in numbers:
            if len(num) == 13:
                return num
        return None

    def toggle_auto_scan(self):
        if self.auto_scan_var.get():
            self.start_auto_scan()
        else:
            self.stop_auto_scan()

    def start_auto_scan(self):
        if not self.scanning and self.current_region:
            self.scanning = True
            self.scan_thread = threading.Thread(target=self.auto_scan_loop)
            self.scan_thread.daemon = True
            self.scan_thread.start()

    def stop_auto_scan(self):
        self.scanning = False
        if self.scan_thread:
            self.scan_thread = None

    def auto_scan_loop(self):
        while self.scanning and self.current_region:
            self.scan_region()
            time.sleep(1)

    def extract_response_message(self, response_text):
        """從XML響應中提取實際信息"""
        try:
            # 使用正則表達式提取<string>標籤中的內容
            match = re.search(r'<string[^>]*>(.*?)</string>', response_text)
            if match:
                return match.group(1)
            return response_text
        except Exception:
            return response_text

    def send_http_request(self, sn):
        """發送HTTP POST請求"""
        try:
            # 暫停掃描
            was_scanning = self.scanning
            self.stop_auto_scan()
            status_msg = "正在發送HTTP請求..."
            self.status_label.config(text=status_msg)
            self.add_log(status_msg)
            
            # 準備POST數據，使用固定的Stage Code值
            data = {
                'pstrUSN': sn,
                'pstrStageCode': 'AP'  # 固定使用AP
            }

            # 發送請求
            response = requests.post(
                "http://f60d.cim.wistron.com/Tester.WebService.api/WebService.asmx/CheckRoute",
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'}
            )

            # 檢查響應
            if response.status_code == 200:
                # 提取並記錄實際響應信息
                response_message = self.extract_response_message(response.text)
                self.add_log(f"收到響應內容: {response_message}")
                
                # 檢查響應中是否包含"ok"
                if "ok" in response.text.lower():
                    self.status_label.config(text="檢查通過")
                    self.show_result_window("PASS")
                else:
                    self.status_label.config(text="檢查未通過")
                    self.show_result_window("NG")
            else:
                status_msg = f"HTTP請求失敗: {response.status_code}"
                self.status_label.config(text=status_msg)
                self.add_log(status_msg)
                # 記錄錯誤響應內容
                error_message = self.extract_response_message(response.text)
                self.add_log(f"錯誤響應內容: {error_message}")
                # 顯示NG視窗
                self.show_result_window("NG")

        except Exception as e:
            error_msg = f"HTTP請求錯誤: {str(e)}"
            self.status_label.config(text=error_msg)
            self.add_log(error_msg)
            messagebox.showerror("錯誤", f"HTTP請求發生錯誤：\n{str(e)}")
            # 顯示NG視窗
            self.show_result_window("NG")
        
        finally:
            # 如果之前在掃描，則恢復掃描
            if was_scanning:
                self.start_auto_scan()
                self.add_log("恢復自動掃描")

    def scan_region(self):
        if not self.current_region:
            return
        
        x, y, width, height = self.current_region
        try:
            # 擷取螢幕區域
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            
            # 將圖片轉換為numpy數組
            img_np = np.array(screenshot)
            
            # 進行OCR識別
            results = self.reader.readtext(img_np)
            
            # 提取文字
            texts = [detection[1] for detection in results]
            
            # 嘗試提取13位數字
            number_13digit = self.extract_13_digit_number(texts)
            
            # 更新狀態
            if number_13digit:
                # 檢查是否與上次不同
                if number_13digit != self.last_number:
                    # 切換顏色
                    self.current_color = "blue" if self.current_color == "black" else "black"
                    # 更新當前數字顯示（使用新顏色）
                    self.number_label.config(text=f"當前SN: {number_13digit}", fg=self.current_color)
                    
                    old_number = self.last_number
                    self.last_number = number_13digit
                    self.save_number_to_file(number_13digit)
                    # 發送HTTP請求
                    self.send_http_request(number_13digit)
                else:
                    self.status_label.config(text="監控中...")
            else:
                self.number_label.config(text="當前SN: 無")
                self.status_label.config(text="未識別到13碼數字")
        except Exception as e:
            error_msg = f"掃描錯誤: {str(e)}"
            self.status_label.config(text=error_msg)
            self.add_log(error_msg)

    def create_float_window(self):
        # 創建浮動窗口
        self.float_window = tk.Toplevel(self.root)
        self.float_window.title("")
        self.float_window.geometry("100x40")
        self.float_window.attributes("-topmost", True)
        self.float_window.resizable(False, False)
        
        # 添加結束按鈕到浮動窗口
        stop_btn = ttk.Button(self.float_window, text="結束框選", command=self.stop_selection)
        stop_btn.pack(padx=5, pady=5)
        
        # 設置窗口位置（右上角）
        screen_width = self.root.winfo_screenwidth()
        self.float_window.geometry(f"+{screen_width-120}+10")

    def stop_selection(self):
        self.stop_auto_scan()
        self.auto_scan_var.set(False)
        self.auto_scan_check.state(['disabled'])
        if self.selection_window:
            self.selection_window.destroy()
            self.selection_window = None
        if self.float_window:
            self.float_window.destroy()
            self.float_window = None
        self.stop_button.config(state="disabled")
        self.start_button.config(state="normal")
        self.status_label.config(text="準備就緒")
        self.current_region = None

    def start_capture(self):
        """開始擷取"""
        self._load_dependencies()  # 確保依賴已載入
        if self._initialize_ocr():
            if not self.capturing:
                self.capturing = True
                self.status_label.config(text="請點擊並拖動滑鼠來選擇區域")
                self.start_button.config(state="disabled")
                self.stop_button.config(state="normal")
                self.auto_scan_check.state(['disabled'])
                
                self.create_overlay()
                self.overlay_window.bind("<Button-1>", self.on_mouse_down)
                self.overlay_window.bind("<B1-Motion>", self.on_mouse_drag)
                self.overlay_window.bind("<ButtonRelease-1>", self.on_mouse_up)

    def create_overlay(self):
        self.overlay_window = tk.Toplevel(self.root)
        self.overlay_window.attributes("-alpha", 0.3, "-topmost", True)
        self.overlay_window.attributes("-fullscreen", True)
        self.overlay_window.configure(bg="gray")

    def create_selection_window(self, x, y, width, height):
        # 如果已存在選擇窗口，先銷毀
        if self.selection_window:
            self.selection_window.destroy()
        
        # 創建新的選擇窗口
        self.selection_window = tk.Toplevel(self.root)
        self.selection_window.overrideredirect(True)
        self.selection_window.attributes("-alpha", 1, "-topmost", True)
        
        # 設置窗口位置和大小
        self.selection_window.geometry(f"{width}x{height}+{x}+{y}")
        
        # 使窗口完全透明
        self.selection_window.attributes('-transparentcolor', 'black')
        
        # 創建畫布並添加紅色邊框
        canvas = tk.Canvas(self.selection_window, width=width, height=height,
                         highlightthickness=0, bg='black')
        canvas.pack(fill='both', expand=True)
        
        # 繪製四條紅色邊框線
        canvas.create_line(0, 0, width, 0, fill='red', width=2)  # 上邊
        canvas.create_line(0, 0, 0, height, fill='red', width=2)  # 左邊
        canvas.create_line(width, 0, width, height, fill='red', width=2)  # 右邊
        canvas.create_line(0, height, width, height, fill='red', width=2)  # 下邊

    def on_mouse_down(self, event):
        self.start_x = event.x
        self.start_y = event.y

    def on_mouse_drag(self, event):
        if self.start_x is not None and self.start_y is not None:
            x1 = min(self.start_x, event.x)
            y1 = min(self.start_y, event.y)
            width = abs(event.x - self.start_x)
            height = abs(event.y - self.start_y)
            
            # 更新選擇窗口
            self.create_selection_window(x1, y1, width, height)

    def on_mouse_up(self, event):
        x1 = min(self.start_x, event.x)
        y1 = min(self.start_y, event.y)
        x2 = max(self.start_x, event.x)
        y2 = max(self.start_y, event.y)
        width = x2 - x1
        height = y2 - y1
        
        # 保存當前區域
        self.current_region = (x1, y1, width, height)
        
        # 執行首次掃描
        self.scan_region()
        
        # 重置狀態
        self.capturing = False
        self.start_button.config(state="normal")
        self.overlay_window.destroy()
        
        # 自動啟動自動掃描
        self.auto_scan_check.state(['!disabled'])
        self.auto_scan_var.set(True)
        self.start_auto_scan()
        
        # 更新主窗口按鈕狀態
        self.stop_button.config(state="normal")

    def show_result_window(self, result_type):
        """顯示結果視窗"""
        ResultWindow(self.root, result_type)

def main():
    # 檢查模型是否已下載
    if not os.path.exists(MODEL_PATH) or not os.listdir(MODEL_PATH):
        print("首次運行，正在下載必要的模型文件...")
        if not download_models():
            messagebox.showerror("錯誤", "模型下載失敗，請確保網絡連接正常並重試。")
            sys.exit(1)
        print("模型下載完成！")
    
    root = tk.Tk()
    app = OCRApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
