[LocalDatabase]
local_host = localhost
local_user = demo
local_password = demo
local_database = WSTC_DEMO

[SerialPorts_RPi]
torque_port = /dev/null
voltage_port = /dev/null
rfid_port = /dev/null
torque_baudrate = 115200
voltage_baudrate = 9600
rfid_baudrate = 9600

[SerialPorts_Windows]
torque_port = DEMO
voltage_port = DEMO
rfid_port = DEMO
torque_baudrate = 115200
voltage_baudrate = 9600
rfid_baudrate = 9600

[System]
platform = auto
ui_update_interval = 0.05
sensor_poll_interval = 0.1
heartbeat_interval = 1.0
queue_max_size = 100
debug_mode = true
demo_mode = true

[TorqueSettings]
data_filter_enabled = true
min_torque_change = 0.1
max_torque_value = 1000
torque_timeout = 5.0

[Network]
webservice_url = http://demo.example.com/WebService.asmx
wifi_check_interval = 30
connection_timeout = 10

[DemoMode]
enabled = true
simulate_sensors = true
simulate_database = true
auto_generate_data = true
demo_data_interval = 2.0
