# 🎨 WSTC UI設計器

## ✨ 功能特色

基於現有WSTC系統架構開發的專業UI設計器，支援拖拉式操作創建使用者介面。

### 🎯 核心功能

| 功能 | 說明 | 狀態 |
|------|------|------|
| **拖拉設計** | 從控件庫拖拉控件到畫布 | ✅ 完成 |
| **即時編輯** | 選中控件即時編輯屬性 | ✅ 完成 |
| **程式碼生成** | 自動生成整合系統的PyQt5程式碼 | ✅ 完成 |
| **檔案管理** | 儲存/載入設計檔案 | ✅ 完成 |
| **UI預覽** | 即時預覽設計效果 | ✅ 完成 |
| **系統整合** | 自動整合WSTC數據流 | ✅ 完成 |

### 🛠️ 支援的控件

#### 基本控件
- 🏷️ **QLabel** - 文字標籤
- 🔘 **QPushButton** - 按鈕
- 📝 **QLineEdit** - 單行輸入框
- 📄 **QTextEdit** - 多行文字編輯器
- 📋 **QComboBox** - 下拉選單
- ☑️ **QCheckBox** - 勾選框
- 🔢 **QSpinBox** - 數字輸入框
- 🎚️ **QSlider** - 滑桿控件
- 📊 **QProgressBar** - 進度條

#### 容器控件 (計劃中)
- 📦 **QGroupBox** - 群組框
- 📑 **QTabWidget** - 標籤頁
- 🖼️ **QFrame** - 框架容器

## 🚀 快速開始

### 1. 啟動設計器

```bash
# 推薦方式：使用啟動腳本
python start_ui_designer.py

# 或直接啟動
python ui_designer.py
```

### 2. 設計UI流程

1. **添加控件**：從左側控件庫拖拉到中央畫布
2. **編輯屬性**：點選控件，在右側面板編輯屬性
3. **調整位置**：拖拉控件調整位置，會自動對齊網格
4. **儲存設計**：檔案 → 儲存 (JSON格式)
5. **生成程式碼**：檔案 → 生成程式碼
6. **預覽效果**：檢視 → 預覽

### 3. 示範程式

```bash
# 執行示範UI
python demo_ui_design.py
```

## 📁 檔案結構

```
wstc_new/
├── ui_designer.py           # 主設計器程式
├── start_ui_designer.py     # 啟動腳本
├── demo_ui_design.py        # 示範程式
├── UI_DESIGNER_GUIDE.md     # 詳細使用指南
└── UI_DESIGNER_README.md    # 本文件
```

## 🔧 系統整合

### 自動整合功能

生成的UI程式碼自動包含：

```python
# 自動導入系統模組
from shared_data import get_data_manager, DeviceType, SensorData
from config import config

class GeneratedUI(QMainWindow):
    def __init__(self):
        super().__init__()
        # 自動連接數據管理器
        self.data_manager = get_data_manager()
        
    def update_data(self):
        """自動生成的數據更新方法"""
        # 整合感測器隊列數據
        if not self.data_manager.sensor_queue.empty():
            sensor_data = self.data_manager.sensor_queue.get_nowait()
            # 處理數據更新UI
```

### 數據流整合

- ✅ **感測器數據**：自動連接 `sensor_queue`
- ✅ **資料庫狀態**：整合 `system_status`
- ✅ **配置管理**：支援 `config` 模組
- ✅ **心跳監控**：整合系統心跳機制

## 🎯 使用場景

### 1. 監控界面設計
- 感測器數據顯示
- 即時狀態監控
- 告警信息展示

### 2. 操作界面創建
- 參數設定界面
- 控制命令面板
- 工作流程界面

### 3. 報表界面建構
- 數據查詢界面
- 統計圖表顯示
- 匯出功能面板

## 📝 程式碼範例

### 生成的UI結構

```python
class MonitorUI(QMainWindow):
    """監控界面 - 設計器生成"""
    
    def __init__(self):
        super().__init__()
        self.data_manager = get_data_manager()
        self.init_ui()
        self.connect_signals()
    
    def init_ui(self):
        """UI初始化"""
        # 控件創建
        self.sensor_label = QLabel(central_widget)
        self.sensor_label.setGeometry(50, 50, 100, 30)
        self.sensor_label.setText('感測器狀態')
        
    def connect_signals(self):
        """信號連接"""
        # 按鈕點擊事件
        self.start_button.clicked.connect(self.start_monitoring)
    
    def update_data(self):
        """數據更新"""
        # 系統整合的數據更新邏輯
        pass
```

## 🏗️ 技術架構

### 設計模式
- **MVC模式**：分離界面、數據、控制邏輯
- **觀察者模式**：屬性變更即時更新
- **命令模式**：支援撤銷/重做操作

### 核心類別
- `UIDesigner`：主設計器視窗
- `DesignCanvas`：設計畫布
- `WidgetPalette`：控件庫
- `PropertyEditor`：屬性編輯器
- `CodeGenerator`：程式碼生成器

## 🔄 開發路線圖

### 已完成 ✅
- [x] 基本拖拉功能
- [x] 屬性即時編輯
- [x] 程式碼自動生成
- [x] 系統整合支援
- [x] 檔案儲存載入

### 計劃中 🚧
- [ ] 容器控件支援
- [ ] 佈局管理器
- [ ] 樣式表編輯器
- [ ] 多國語言支援
- [ ] 模板系統

### 未來版本 🌟
- [ ] 可視化事件編輯
- [ ] 即時程式碼預覽
- [ ] 元件庫擴展
- [ ] 協作設計功能

## 💡 設計理念

### 簡單易用
- 直觀的拖拉操作
- 即時的視覺回饋
- 清晰的界面結構

### 系統整合
- 完美整合WSTC架構
- 自動生成系統相容程式碼
- 無縫數據流連接

### 專業品質
- 生成高品質PyQt5程式碼
- 遵循編程最佳實踐
- 支援大型專案開發

---

**🎨 開始設計您的專業UI界面！**

使用WSTC UI設計器，讓界面設計變得簡單而高效。