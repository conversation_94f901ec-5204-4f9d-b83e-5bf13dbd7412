#!/bin/bash

echo "========================================"
echo "WSTC 高效能測試系統 - 快速啟動"
echo "========================================"
echo

# 檢查 Python 環境
echo "檢查 Python 環境..."
if ! command -v python3 &> /dev/null; then
    echo "錯誤: 未找到 Python3，請先安裝 Python 3.6+"
    exit 1
fi

# 檢查 PyQt5 安裝
echo "檢查 PyQt5 安裝..."
if ! python3 -c "import PyQt5" &> /dev/null; then
    echo "警告: PyQt5 未安裝，正在安裝..."
    
    # 嘗試使用包管理器安裝
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y python3-pyqt5
    elif command -v yum &> /dev/null; then
        sudo yum install -y python3-qt5
    elif command -v dnf &> /dev/null; then
        sudo dnf install -y python3-qt5
    else
        # 使用 pip 安裝
        pip3 install PyQt5>=5.15.0
    fi
    
    # 再次檢查
    if ! python3 -c "import PyQt5" &> /dev/null; then
        echo "錯誤: PyQt5 安裝失敗"
        exit 1
    fi
fi

echo
echo "選擇啟動模式:"
echo "1. 簡化模式 (推薦，線程版本)"
echo "2. 完整模式 (多進程版本)"
echo "3. 測試模式 (功能測試)"
echo
read -p "請選擇 (1-3): " choice

case $choice in
    1)
        echo "啟動簡化模式..."
        python3 main_simple.py
        ;;
    2)
        echo "啟動完整模式..."
        python3 main.py
        ;;
    3)
        echo "運行測試..."
        python3 tests/test_qt5.py
        echo "按任意鍵繼續..."
        read -n 1
        python3 tests/test_startup_flow.py
        ;;
    *)
        echo "無效選擇，使用默認簡化模式..."
        python3 main_simple.py
        ;;
esac

echo
echo "系統已關閉"
