"""
簡化版主啟動程式 - 使用線程而非多進程
解決 Windows 多進程權限問題
"""
import threading
import time
import signal
import sys
import os

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from shared_data import get_data_manager
from config import config

class SimpleSystemManager:
    """簡化系統管理器 - 使用線程"""
    
    def __init__(self):
        self.threads = []
        self.data_manager = get_data_manager()
        self.running = False
        self.ui_app = None
    
    def start(self):
        """啟動所有系統組件"""
        print("="*60)
        print("高效能實時感測器系統啟動中... (簡化版)")
        print("="*60)
        
        self.running = True
        
        try:
            # 1. 首先啟動主UI (在主線程中)
            print("正在啟動 UI 界面...")
            self._start_ui()
            
        except Exception as e:
            print(f"系統啟動失敗: {e}")
            self.stop()
    
    def _start_ui(self):
        """在主線程中啟動 UI"""
        try:
            from main_ui import main
            
            # 啟動後台組件線程
            self._start_background_components()
            
            print("✓ UI 界面啟動中...")
            
            # 在主線程中運行 UI
            main()
            
        except KeyboardInterrupt:
            print("收到中斷信號，正在關閉...")
        except Exception as e:
            print(f"UI 錯誤: {e}")
        finally:
            self.stop()
    
    def _start_background_components(self):
        """啟動後台組件線程"""
        # 等待一下讓 UI 先初始化
        def delayed_start():
            time.sleep(2)  # 等待 UI 初始化
            
            # 啟動感測器管理器線程
            sensor_thread = threading.Thread(
                target=self._run_sensor_manager,
                name="SensorManager",
                daemon=True
            )
            sensor_thread.start()
            self.threads.append(sensor_thread)
            
            time.sleep(1)
            
            # 啟動資料庫記錄器線程
            db_thread = threading.Thread(
                target=self._run_database_logger,
                name="DatabaseLogger",
                daemon=True
            )
            db_thread.start()
            self.threads.append(db_thread)

            time.sleep(1)

            # 啟動資料庫同步器線程
            sync_thread = threading.Thread(
                target=self._run_database_sync,
                name="DatabaseSync",
                daemon=True
            )
            sync_thread.start()
            self.threads.append(sync_thread)
        
        # 在後台線程中啟動組件
        startup_thread = threading.Thread(target=delayed_start, daemon=True)
        startup_thread.start()
    
    def _run_sensor_manager(self):
        """運行感測器管理器線程"""
        try:
            from sensor_manager import SensorManager
            
            print("啟動感測器管理器線程...")
            manager = SensorManager()
            manager.start()
            
            # 保持運行
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("感測器管理器收到停止信號")
        except Exception as e:
            print(f"感測器管理器錯誤: {e}")
        finally:
            if 'manager' in locals():
                try:
                    manager.stop()
                except:
                    pass
    
    def _run_database_logger(self):
        """運行資料庫記錄器線程"""
        try:
            from database_logger import DatabaseLogger
            
            print("啟動資料庫記錄器線程...")
            logger = DatabaseLogger()
            logger.initialize_ssid_table()
            logger.start()
            
            # 保持運行
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("資料庫記錄器收到停止信號")
        except Exception as e:
            print(f"資料庫記錄器錯誤: {e}")
        finally:
            if 'logger' in locals():
                try:
                    logger.stop()
                except:
                    pass

    def _run_database_sync(self):
        """運行資料庫同步器線程"""
        try:
            from database_sync import DatabaseSynchronizer

            print("啟動資料庫同步器線程...")
            synchronizer = DatabaseSynchronizer()
            synchronizer.start()

            # 保持運行
            while self.running:
                time.sleep(1)

        except KeyboardInterrupt:
            print("資料庫同步器收到停止信號")
        except Exception as e:
            print(f"資料庫同步器錯誤: {e}")
        finally:
            if 'synchronizer' in locals():
                try:
                    synchronizer.stop()
                except:
                    pass
    
    def stop(self):
        """停止所有系統組件"""
        if not self.running:
            return
        
        print("正在停止系統...")
        self.running = False
        
        # 設置關閉標誌
        try:
            self.data_manager.system_status['shutdown_requested'] = True
        except:
            pass
        
        # 等待線程結束
        for thread in self.threads:
            if thread.is_alive():
                print(f"等待線程結束: {thread.name}")
                thread.join(timeout=3)
        
        print("系統已停止")
    
    def signal_handler(self, signum, frame):
        """信號處理器"""
        print(f"\n收到信號 {signum}，正在關閉系統...")
        self.stop()
        sys.exit(0)

def main():
    """主函數"""
    # 設置信號處理
    manager = SimpleSystemManager()
    signal.signal(signal.SIGINT, manager.signal_handler)
    signal.signal(signal.SIGTERM, manager.signal_handler)
    
    try:
        # 檢查配置
        print("檢查系統配置...")
        db_config = config.get_db_config()
        serial_config = config.get_serial_config()
        
        print(f"資料庫: {db_config['host']}:{db_config['database']}")
        print(f"串口配置: {len(serial_config)} 個設備")
        
        # 啟動系統
        manager.start()
        
    except KeyboardInterrupt:
        print("\n用戶中斷")
    except Exception as e:
        print(f"系統錯誤: {e}")
        import traceback
        traceback.print_exc()
    finally:
        manager.stop()

if __name__ == "__main__":
    main()
