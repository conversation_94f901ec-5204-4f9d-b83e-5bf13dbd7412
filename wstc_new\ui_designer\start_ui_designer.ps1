# WSTC UI設計器啟動腳本 (PowerShell版本)
# 解決Windows PowerShell的語法問題

Write-Host "🎨 正在啟動WSTC UI設計器..." -ForegroundColor Green
Write-Host "📁 設計器位置: $PSScriptRoot\ui_designer" -ForegroundColor Cyan

# 檢查Python是否可用
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python版本: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 找不到Python，請確保Python已安裝並加入PATH" -ForegroundColor Red
    exit 1
}

# 切換到專案目錄
Set-Location $PSScriptRoot

# 啟動UI設計器
try {
    Write-Host "🚀 啟動UI設計器..." -ForegroundColor Yellow
    python start_ui_designer.py
} catch {
    Write-Host "❌ 啟動失敗: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 請確保已安裝所需依賴: pip install -r requirements.txt" -ForegroundColor Yellow
    exit 1
} 