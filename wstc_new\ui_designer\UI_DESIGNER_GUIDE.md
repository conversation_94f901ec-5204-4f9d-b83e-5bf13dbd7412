# WSTC UI設計器使用指南

## 🎯 概述

WSTC UI設計器是一個基於現有系統架構的拖拉式UI建構工具，讓您可以視覺化地設計使用者介面，並自動生成整合到系統中的PyQt5程式碼。

## 🚀 啟動方式

```bash
# 方法1: 直接啟動設計器
python start_ui_designer.py

# 方法2: 直接執行主程式
python ui_designer.py
```

## 📱 主要功能

### 1. **拖拉式控件設計**
- 從左側控件庫拖拉控件到中央畫布
- 支援的控件類型：
  - 🏷️ QLabel (標籤)
  - 🔘 QPushButton (按鈕)
  - 📝 QLineEdit (單行輸入)
  - 📄 QTextEdit (多行輸入)
  - 📋 QComboBox (下拉選單)
  - ☑️ QCheckBox (勾選框)
  - 🔢 QSpinBox (數字框)
  - 🎚️ QSlider (滑桿)
  - 📊 QProgressBar (進度條)

### 2. **即時屬性編輯**
- 點選控件查看/編輯屬性
- 支援編輯：
  - 物件名稱
  - 位置 (X, Y座標)
  - 大小 (寬度, 高度)
  - 文字內容
  - 顏色設定
  - 字體設定

### 3. **程式碼自動生成**
- 生成完整的PyQt5程式碼
- 自動整合到WSTC系統架構
- 包含系統模組導入
- 支援數據流整合

### 4. **設計檔案管理**
- 儲存設計為JSON格式
- 載入現有設計檔案
- 支援另存為功能

### 5. **UI預覽功能**
- 即時預覽設計效果
- 測試控件互動

## 🛠️ 操作步驟

### 步驟1: 設計UI佈局
1. 從左側控件庫選擇需要的控件
2. 拖拉到中央畫布的目標位置
3. 重複添加所需控件

### 步驟2: 調整控件屬性
1. 點選畫布上的控件進行選取
2. 在右側屬性編輯器修改屬性
3. 即時查看變更效果

### 步驟3: 儲存設計
1. 選單 → 檔案 → 儲存 (或 Ctrl+S)
2. 選擇儲存位置和檔案名稱
3. 設計會以JSON格式儲存

### 步驟4: 生成程式碼
1. 選單 → 檔案 → 生成程式碼
2. 輸入UI類別名稱
3. 查看/編輯生成的程式碼
4. 儲存為Python檔案

### 步驟5: 預覽UI
1. 選單 → 檢視 → 預覽
2. 在新視窗中查看UI效果

## 🎨 設計技巧

### 網格對齊
- 預設啟用網格對齊功能
- 控件會自動對齊到10px網格
- 可透過選單切換網格顯示

### 控件選取和移動
- 點選控件進行選取（出現紅色虛線邊框）
- 拖拉選中的控件改變位置
- Delete鍵刪除選中控件

### 精確定位
- 使用右側屬性編輯器精確設定位置
- 支援像素級精確調整

## 📋 生成的程式碼結構

```python
"""
自動生成的UI程式碼
基於wstc_new系統架構
"""
import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 導入系統模組
from shared_data import get_data_manager, DeviceType, SensorData
from config import config

class GeneratedUI(QMainWindow):
    """自動生成的UI類別"""
    
    def __init__(self):
        super().__init__()
        self.data_manager = get_data_manager()
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 控件創建和配置
        
    def update_data(self):
        """更新數據顯示"""
        # 整合到系統數據流
```

## 🔧 系統整合

### 數據流整合
生成的UI自動包含：
- `get_data_manager()` 數據管理器
- `sensor_queue` 感測器數據隊列
- `update_data()` 數據更新方法

### 配置整合
- 自動導入 `config` 模組
- 支援系統配置讀取

### 事件處理
生成的程式碼包含：
- `connect_signals()` 信號連接方法
- 預留事件處理框架

## 📁 檔案說明

| 檔案 | 說明 |
|------|------|
| `ui_designer.py` | 主設計器程式 |
| `start_ui_designer.py` | 快速啟動腳本 |
| `*.json` | 設計檔案 |
| `generated_*.py` | 生成的UI程式碼 |

## ⚡ 效能優化

### 畫布效能
- 支援大量控件設計
- 優化的拖拉操作
- 即時屬性更新

### 程式碼生成
- 生成高效的PyQt5程式碼
- 整合系統架構模式
- 支援數據流最佳化

## 🔍 常見問題

### Q: 如何刪除控件？
A: 選中控件後按Delete鍵，或使用選單→編輯→刪除

### Q: 如何調整控件大小？
A: 在右側屬性編輯器的"大小"欄位調整寬度和高度

### Q: 生成的程式碼如何整合到系統？
A: 生成的程式碼已自動導入系統模組，可直接在WSTC環境中運行

### Q: 能否匯入現有UI設計？
A: 可以開啟之前儲存的JSON設計檔案

### Q: 支援哪些控件類型？
A: 目前支援常用的PyQt5基礎控件，未來會擴展更多類型

## 🎯 最佳實踐

1. **命名規範**: 使用有意義的控件名稱
2. **佈局規劃**: 先規劃整體佈局再添加細節
3. **屬性設定**: 合理設定控件屬性提升使用體驗
4. **程式碼整合**: 生成程式碼後根據需求調整業務邏輯
5. **定期儲存**: 設計過程中定期儲存避免丟失

---

**🚀 開始設計您的專屬UI介面吧！**