import traceback
import threading
import time
import tkinter as tk
import tkinter.font as tkFont
import asyncio, evdev
import os,sys
from tkinter import *
import pymysql
import json
from tkinter import ttk
import datetime

import requests
from xml.dom.minidom import parseString

bg_blue = "#CCFFFF"
bg_white = "#FFFFFF"
top = tk.Tk()
top.geometry('1920x1080')
top.config(background=bg_blue)
top.overrideredirect(True)
top.focus_set() 
ft1 = tkFont.Font(family='Times',size=75,weight='bold')
ft2= tkFont.Font(family='Times',size=45,weight='bold')
ft3= tkFont.Font(family='Times',size=45,weight='bold')
ft4 = tkFont.Font(family='Times',size=30)
ft5 = tkFont.Font(family='Times',size=25)
ft6 = tkFont.Font(family='Times',size=32)

btn_end = tk.But<PERSON>(top,text="返回",font=ft3)
btn_end.place(x=1700,y=30,width=160,height=100)
btn_end["command"] = top.destroy

btn_sure= tk.But<PERSON>(top,text="確認",font=ft3)
btn_sure.place(x=1250,y=350,width=160,height=180)

txt_log = tk.Text(bg=bg_white,font=ft6,fg="#333333",relief="solid")
txt_log.place(x=50,y=550,width=1300,height=450)
txt_log_scro = tk.Scrollbar(txt_log,orient="vertical", command=txt_log.yview)
txt_log_scro.pack(side="right", fill="y")

label_display=tk.Label(top,text="當前扭力",bg=bg_blue,font=ft2,fg='#333333',justify="center")
label_display.place(x=1500,y=300,width=300,height=100)

label_power=tk.Label(top,bg=bg_white,font=ft2,fg="#333333",justify="center",relief="solid")#label框
label_power.place(x=1500,y=400,width=300,height=300)

label_name=tk.Label(top,text='校验人',bg=bg_blue,font=ft2,fg='#333333',justify="center")
label_name.place(x=50,y=80,width=280,heigh=80,)

label_oder=tk.Label(top,text='工單',bg=bg_blue,font=ft2,fg='#333333',justify="center")
label_oder.place(x=400,y=80,width=280,heigh=80)#線別的label大小

label_sceewdriver=tk.Label(top,text='電/氣槍',bg=bg_blue,font=ft2,fg='#333333',justify="center")
label_sceewdriver.place(x=750,y=80,width=280,heigh=80)#線別的label大小

label_customer=tk.Label(top,text='客户',bg=bg_blue,font=ft2,fg='#333333',justify="center")
label_customer.place(x=1100,y=80,width=280,heigh=80,)

remotehost = 'localhost'
local_db = 'WSTC'
op_name = StringVar()

label_op=tk.Label(top,fg="#000000",relief="solid",textvariable=op_name,font=ft4,justify="center")#label框
label_op.config(background='white')
label_op.place(x=50,y=180,width=300,heigh=70)

customer = StringVar()
cbx_customer=tk.Label(top,fg="#333333",relief="solid",textvariable= customer ,font=ft4,justify="center")
cbx_customer.place(x=1100,y=180,width=300,heigh=70)

label_model=tk.Label(top,text='機種',bg=bg_blue,font=ft2,fg='#333333',justify="center")
label_model.place(x=1450,y=80,width=220,heigh=80)#機種的label大小位置

sceewdriver = StringVar()
label_sceewdriver=tk.Label(top,fg="#000000",relief="solid",textvariable= sceewdriver ,font=ft4,justify="center")#label框
label_sceewdriver.config(background='white')
label_sceewdriver.place(x=750,y=180,width=300,heigh=70)

oder = StringVar()
label_oder=tk.Label(top,fg="#000000",relief="solid",textvariable=oder,font=ft4,justify="center")#label框
label_oder.config(background='white')
label_oder.place(x=400,y=180,width=300,heigh=70)

label_station=tk.Label(top,text='站別',bg=bg_blue,font=ft2,fg='#000000',justify="center")
label_station.place(x=50,y=280,width=230,heigh=80)#站別的label大小位置

model = StringVar()
cbx_model =tk.Label(top,fg="#333333",relief="solid",textvariable= model ,font=ft4,justify="center")
#cbx_model = ttk.Combobox(top,textvariable=model,font=ft4,justify="center",height=10,state='readonly',value='0')   
cbx_model.place(x=1450,y=180,width=300,heigh=70) #機種的commbobox大小

station = StringVar()
cbx_station = ttk.Combobox(top,textvariable=station,font=ft4,justify="center",height=10,state='readonly',value='0') 
cbx_station.place(x=50,y=380,width=300,heigh=70) #站別的commbobox大小

def GetMoInfoByMo(MO, StageCode, InfoName):
    url = 'http://************:138/Tester.WebserviceNewVersion/WebService.asmx?op=GetMoInfoByMo'
    
    payload = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <GetMoInfoByMo xmlns="http://localhost/Tester.WebService/WebService">
      <MO>""" + MO + """</MO>
      <StageCode>""" + StageCode + """</StageCode>
      <InfoName>""" + InfoName + """</InfoName>
    </GetMoInfoByMo>
  </soap:Body>
</soap:Envelope>"""
                
    headers = {'Content-Type': 'text/xml; charset=utf-8'}
    # POST request
    
    response = requests.request("POST", 
                                    url,
                                    headers=headers, 
                                    data=payload, 
                                    verify=False)
    if response.status_code != 200:
        raise ConnectionError(f'{url} status code is {response.status_code}.')
    response = response.text
    doc = parseString(response)
    collection = doc.documentElement
    result = collection.getElementsByTagName('GetMoInfoByMoResult')[0].childNodes[0].data    
    return result

def choose_model():

    value1 = customer.get()  # 选中的值


    if value1!='':
        remotehost = 'localhost'
        local_db = 'WSTC'
        connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
        curl = connl.cursor()   #鼠标光标吗？
        curl.execute("SELECT MACHINE_MODEL from INFO WHERE CUSTOMER=%s",value1)
        model_list = [i[0] for i in curl.fetchall()]
        model_list = list(set(model_list))
        cbx_model['value'] = model_list
        connl.close()
#endregion
def chooe_station():
    
    value1 = customer.get()# 选中的值
    value2 = model.get()
    if value1!='' and value2!='':
        connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
        curl = connl.cursor()   #鼠标光标吗？
        curl.execute("SELECT STATION from INFO WHERE CUSTOMER=%s AND MACHINE_MODEL=%s",(value1,value2))
        station_table = [i[0] for i in curl.fetchall()]
        station_list = station_table[0]
        station_list = station_list.split(',')        
        cbx_station['value'] = station_list
        curl.close()
        connl.close()
def work_():
    time_ = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))
    information = '['+time_+'] 請掃工單 \n'
    txt_log.insert(0.0,information)
    txt_log.update()
    while True:
        connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
        curl = connl.cursor()    
        curl.execute("select DEVICE,CURRENT_VALUE,FLAG from SSID_TABLE")
        ssid_table = curl.fetchall()
        connl.commit()
        connl.close()
        curl.close()
        oder_flag= ssid_table[8][2]
        oder_value=ssid_table[8][1]
        if oder_flag =='1':
            
            connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
            curl = connl.cursor()
            curl.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('0','WORK'))
            connl.commit()
            curl.close()
            connl.close()
            oder.set(oder_value)
            #customer.set("Yama")
            
            customer.set(GetMoInfoByMo(oder_value,"SN", "CUSTOMER"))#("Yama")#
            model.set(GetMoInfoByMo(oder_value,"SN", "MODEL"))

            
            break
def op_():
    time_ = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))
    information = '['+time_+'] 請刷廠牌 \n'
    txt_log.insert(0.0,information)
    txt_log.update()
    
    while True:
        connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
        curl = connl.cursor()
        curl.execute("SELECT DEVICE,CURRENT_VALUE,FLAG FROM SSID_TABLE")
        ssid_table = curl.fetchall()
        connl.commit()
        connl.close()
        curl.close()
        RFID_value = ssid_table[1][1]
        RFID_flag = ssid_table[1][2]
        
        if RFID_flag =='1':
            connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
            curl = connl.cursor()
            curl.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('0','RFID'))
                   
            curl.execute('SELECT NAME FROM TEST_MEMBER WHERE RFID_NUMBER=%s',RFID_value)
            name = str(curl.fetchall()[0][0])
            
            connl.commit()
            curl.close()
            connl.close()
            op_name.set(name)
            
            break

    

def scrw_():
    time_ = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))
    information = '['+time_+'] 請掃電/氣槍二維碼 \n'
    txt_log.insert(0.0,information)
    txt_log.update()

    while True:
        connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
        curl = connl.cursor()    
        curl.execute("select DEVICE,CURRENT_VALUE,FLAG from SSID_TABLE")
        ssid_table = curl.fetchall()
        connl.commit()
        connl.close()
        curl.close()
        scrwdrver_value = ssid_table[7][1]
        scrwdrver_flag = ssid_table[7][2]

        if scrwdrver_flag =='1':
            
            connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
            curl = connl.cursor()
            curl.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('0','SCREWDRIVER'))
            connl.commit()
            curl.close()
            connl.close()
            sceewdriver.set(scrwdrver_value)
            
            scrwdrver_on='1'
            chooe_station()
            
            break


cbx_model.bind('<<ComboboxSelected>>', chooe_station)




def power_data():
    customer_value = customer.get()   
    model_value = model.get()
    station_value = cbx_station.get()

    connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
    curl = connl.cursor() 
    curl.execute("select POWER_MIN,POWER_MAX,STATION FROM INFO WHERE CUSTOMER=%s AND MACHINE_MODEL=%s ",(customer_value,model_value))
    value_table = curl.fetchall()
    
    power_min_list =value_table[0][0].split(',')
    power_max_list =value_table[0][1].split(',')
    station_list =value_table[0][2].split(',')
    
    station_count=station_list.index(station_value)
    power_min_value=power_min_list[station_count]
    power_max_value=power_max_list[station_count]
    time_ = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))
    information = '['+time_+']  '+'当前为:'+customer_value+' '+model_value+' '+station_value+'的电气枪，SOP:'+str(power_min_value)+'~'+str(power_max_value)+' 请开始校准! \n'
    txt_log.insert(0.0,information)
    txt_log.update()
    
    curl.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('0','POWER'))
    
    curl.execute('SELECT FLAG from SSID_TABLE WHERE DEVICE=%s',('POWER'))
    power_state = curl.fetchall()[0][0]
    connl.commit()
    connl.close()
    count = 0
    rdb_dict = {}
    
    while True:
        try:
            connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
            curl = connl.cursor() 
            curl.execute("select CURRENT_VALUE,FLAG from SSID_TABLE WHERE DEVICE='POWER'")
            value_table = curl.fetchall()
            power_value = float(value_table[0][0])
            power_flag = value_table[0][1]
            connl.close()
            continue_flag = False
            power_min_value =float(power_min_value)
            power_max_value =float(power_max_value)

            if power_flag == '1' and ((power_value >= power_min_value) and (power_value <= power_max_value)):
                try:
                    customer_value = customer.get()   
                    model_value =  model.get()
                    station_value = cbx_station.get()
                    sceewdriver_value =sceewdriver.get()
                    oder_va = oder.get()
                    connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
                    curl = connl.cursor()
                    curl.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('0','POWER'))
                    connl.commit()
                    #将测量记录塞入本地数据库

                    rdb_dict['CURRENT_POWER'] = power_value
                    rdb_dict['POWER_MAX'] = power_max_value
                    rdb_dict['POWER_MIN'] = power_min_value
                    rdb_dict['STATION'] = station_value
                    rdb_dict['CID'] = '--'
                    rdb_dict['CUSTOMER'] = customer_value
                    rdb_dict['MACHINE_MODEL'] = model_value
                    rdb_dict['PLANT'] = 'WZS-6'
                    rdb_dict['SCREWDRIVER'] = sceewdriver_value
                    rdb_dict['WORK_ORDER'] = oder_va
                    rdb_dict['OPS']= '新增檢測'
                    rdb_dict['RESULT'] = 'OK'
                    rdb_dict['REMARKS']= '--'
                    rdb_dict['NO']= '#3'
                    time_ = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))
                   # rdb_dict['UTIME'] = time_
                    data = json.dumps(rdb_dict)
                    curl.execute('INSERT INTO BUFFER_RDB (RDB_TABLE,VALUE) VALUES (%s,%s)', ('HISTORY',data))
                    connl.commit()
                    connl.close()
                    curl.close()
                    count = count+1
                    label_power["text"] = power_value
                    information = '['+time_+']  '+'扭力校验合格'+str(count)+'次 \n'
                    txt_log.insert(0.0,information)
                    txt_log.update()
                
                except:
                    print(traceback.format_exc())
            
                
            elif power_flag == '1' and (power_value <= power_min_value or power_value >= power_max_value):
                
                label_power["text"] = power_value
                time_ = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))
                information = '['+time_+']  '+'扭力不符合SOP，请重新校验 \n'
                txt_log.insert(0.0,information)
                txt_log.update()
                connl = pymysql.connect(host=remotehost,port = 3306,user='power',passwd='1q2w3e4r',db = local_db ,charset = 'utf8')
                curl = connl.cursor()
                curl.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('0','POWER'))
                connl.commit()
                rdb_dict['CURRENT_POWER'] = power_value
                rdb_dict['POWER_MAX'] = power_max_value
                rdb_dict['POWER_MIN'] = power_min_value
                rdb_dict['STATION'] = station_value
                rdb_dict['CID'] = '--'
                rdb_dict['CUSTOMER'] = customer_value
                rdb_dict['MACHINE_MODEL'] = model_value
                rdb_dict['PLANT'] = 'WZS-6'
                rdb_dict['SCREWDRIVER'] = sceewdriver_value
                rdb_dict['WORK_ORDER'] = oder_va
                rdb_dict['OPS']= '新增檢測'
                rdb_dict['RESULT'] = 'NG'
                rdb_dict['REMARKS']= '--'
                rdb_dict['NO']= '#3'
                data = json.dumps(rdb_dict)
                curl.execute('INSERT INTO BUFFER_RDB (RDB_TABLE,VALUE) VALUES (%s,%s)', ('HISTORY',data))
                connl.commit()
                connl.close()
                curl.close()
                count = 0
            
            if count == 3:
                time_ = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))
                information ='['+time_+']  '+'本支电新增完成 \n'
                txt_log.insert(0.0,information)
                txt_log.update()
                
                break
    
        except:

            a=2
                    
    
    
btn_sure['command']= power_data
top.option_add("*TCombobox*Listbox.font",ft5)
op_()
work_()
scrw_()

top.mainloop()

