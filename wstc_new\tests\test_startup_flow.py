#!/usr/bin/env python3
"""
測試新的啟動流程
驗證 UI 優先啟動和狀態顯示功能
"""
import sys
import time
import threading
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QTextEdit, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 模擬數據管理器
class MockDataManager:
    def __init__(self):
        self.system_status = {
            'sensor_active': False,
            'ui_active': False,
            'ui_ready': False,
            'db_logger_active': False,
            'shutdown_requested': False
        }
        self.heartbeats = {
            'sensor_manager': time.time(),
            'ui': time.time(),
            'db_logger': time.time()
        }
    
    def update_heartbeat(self, component):
        self.heartbeats[component] = time.time()

class TestStartupUI(QMainWindow):
    """測試啟動流程的簡化 UI"""
    
    def __init__(self):
        super().__init__()
        self.data_manager = MockDataManager()
        self.running = True
        self.initUI()
        self.start_simulation()
    
    def initUI(self):
        """初始化 UI"""
        self.setWindowTitle("WSTC 啟動流程測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央 widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 創建佈局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 標題
        title = QLabel("WSTC 高效能測試系統 - 啟動流程測試")
        title.setFont(QFont('Arial', 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 訊息區域
        self.message_area = QTextEdit()
        self.message_area.setFont(QFont('Consolas', 10))
        self.message_area.setReadOnly(True)
        layout.addWidget(self.message_area)
        
        # 顯示初始訊息
        self.show_message("UI 系統已啟動")
        self.show_message("正在啟動其他系統組件...")
    
    def show_message(self, message):
        """顯示訊息"""
        time_str = time.strftime('%H:%M:%S')
        full_message = f"[{time_str}] {message}"
        self.message_area.append(full_message)
        
        # 自動滾動到底部
        scrollbar = self.message_area.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def start_simulation(self):
        """開始模擬啟動流程"""
        # 設置 UI 準備狀態
        self.data_manager.system_status['ui_ready'] = True
        self.data_manager.system_status['ui_active'] = True
        
        # 啟動狀態監控
        status_thread = threading.Thread(target=self.status_monitor, daemon=True)
        status_thread.start()
        
        # 啟動模擬組件
        simulation_thread = threading.Thread(target=self.simulate_components, daemon=True)
        simulation_thread.start()
    
    def status_monitor(self):
        """狀態監控線程"""
        startup_messages_sent = set()
        
        while self.running:
            try:
                system_status = self.data_manager.system_status
                
                # 檢查感測器管理器狀態
                if system_status.get('sensor_active', False) and 'sensor' not in startup_messages_sent:
                    self.show_message("✓ 感測器管理器已啟動")
                    self.show_message("  - 扭力感測器連接中...")
                    self.show_message("  - RFID 讀取器連接中...")
                    self.show_message("  - 電壓感測器連接中...")
                    startup_messages_sent.add('sensor')
                
                # 檢查資料庫記錄器狀態
                if system_status.get('db_logger_active', False) and 'db' not in startup_messages_sent:
                    self.show_message("✓ 資料庫記錄器已啟動")
                    self.show_message("  - 資料庫連接已建立")
                    self.show_message("  - 數據記錄服務已就緒")
                    startup_messages_sent.add('db')
                
                # 檢查是否所有組件都已啟動
                if (system_status.get('sensor_active', False) and 
                    system_status.get('db_logger_active', False) and 
                    'complete' not in startup_messages_sent):
                    self.show_message("=" * 50)
                    self.show_message("✓ 系統啟動完成！")
                    self.show_message("響應時間：1-3ms (Qt5 優化版本)")
                    self.show_message("系統已準備就緒，可以開始測試")
                    self.show_message("=" * 50)
                    startup_messages_sent.add('complete')
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"狀態監控錯誤: {e}")
                time.sleep(1)
    
    def simulate_components(self):
        """模擬組件啟動"""
        try:
            # 模擬感測器管理器啟動
            time.sleep(2)
            self.data_manager.system_status['sensor_active'] = True
            
            # 模擬資料庫記錄器啟動
            time.sleep(1.5)
            self.data_manager.system_status['db_logger_active'] = True
            
            # 模擬心跳更新
            while self.running:
                self.data_manager.update_heartbeat('sensor_manager')
                self.data_manager.update_heartbeat('ui')
                self.data_manager.update_heartbeat('db_logger')
                time.sleep(1)
                
        except Exception as e:
            print(f"組件模擬錯誤: {e}")
    
    def closeEvent(self, event):
        """關閉事件"""
        self.running = False
        self.show_message("測試結束")
        event.accept()

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式屬性
    app.setApplicationName("WSTC 啟動流程測試")
    
    # 創建測試 UI
    test_ui = TestStartupUI()
    test_ui.show()
    
    print("啟動流程測試開始...")
    print("請觀察 UI 界面中的啟動狀態訊息")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("測試中斷")
    finally:
        test_ui.running = False

if __name__ == "__main__":
    main()
