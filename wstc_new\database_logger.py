"""
資料庫記錄器 - 背景執行
負責將感測器數據記錄到資料庫，不影響即時顯示性能
"""
import threading
import time
import pymysql
import json
import traceback
from typing import Dict, Any

from shared_data import get_data_manager, DeviceType, SensorData
from config import config
import datetime

class DatabaseLogger:
    """資料庫記錄器"""
    
    def __init__(self):
        self.data_manager = get_data_manager()
        self.db_config = config.get_db_config()
        self.running = False
        self.connection_pool = []
        self.pool_size = 3
        
        # 遠端資料庫同步配置
        self.remote_sync_enabled = True
        self.remote_sync_interval = 2  # 秒
        self.remote_config = None
        self.last_sync_time = 0
        
        # 初始化連接池
        self._init_connection_pool()
        
        # 載入遠端資料庫配置
        self._load_remote_config()
    
    def _init_connection_pool(self):
        """初始化資料庫連接池"""
        try:
            for _ in range(self.pool_size):
                conn = pymysql.connect(**self.db_config)
                self.connection_pool.append(conn)
            print(f"資料庫連接池初始化完成 ({self.pool_size} 個連接)")
        except Exception as e:
            print(f"資料庫連接池初始化失敗: {e}")
    
    def _get_connection(self):
        """從連接池獲取連接"""
        if self.connection_pool:
            return self.connection_pool.pop()
        else:
            # 如果池為空，建立新連接
            return pymysql.connect(**self.db_config)
    
    def _return_connection(self, conn):
        """歸還連接到連接池"""
        if len(self.connection_pool) < self.pool_size:
            self.connection_pool.append(conn)
        else:
            conn.close()
    
    def _load_remote_config(self):
        """載入遠端資料庫配置"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT _KEY, _VALUE FROM _SYS")
            config_data = cursor.fetchall()
            
            remote_config = {}
            for key, value in config_data:
                remote_config[key] = value
            
            # 檢查必要的配置項目
            required_keys = ['HOST_RDB', 'PORT_RDB', 'USER_RDB', 'PASSWD_RDB', 'BACKUP_DB']
            if all(key in remote_config for key in required_keys):
                self.remote_config = {
                    'host': remote_config['HOST_RDB'],
                    'port': int(remote_config['PORT_RDB']),
                    'user': remote_config['USER_RDB'],
                    'password': remote_config['PASSWD_RDB'],
                    'database': remote_config['BACKUP_DB'],
                    'charset': 'utf8'
                }
                print("遠端資料庫配置載入成功")
            else:
                print("遠端資料庫配置不完整，將使用預設配置")
                # 使用預設的遠端配置 (從原始程式碼中提取)
                self.remote_config = {
                    'host': '***********',
                    'port': 3306,
                    'user': 'fa',
                    'password': '(1q2w3e4r)',
                    'database': 'WZS6_HISTORY_POWER',
                    'charset': 'utf8'
                }
            
            cursor.close()
            self._return_connection(conn)
            
        except Exception as e:
            print(f"載入遠端資料庫配置失敗: {e}")
            self.remote_sync_enabled = False
    
    def start(self):
        """啟動資料庫記錄器"""
        print("資料庫記錄器啟動中...")
        self.running = True
        
        # 更新系統狀態
        self.data_manager.system_status['db_logger_active'] = True
        
        # 啟動工作線程
        worker_thread = threading.Thread(target=self._worker, daemon=True)
        worker_thread.start()
        
        # 啟動心跳線程
        heartbeat_thread = threading.Thread(target=self._heartbeat_worker, daemon=True)
        heartbeat_thread.start()
        
        # 啟動遠端同步線程
        if self.remote_sync_enabled and self.remote_config:
            remote_sync_thread = threading.Thread(target=self._remote_sync_worker, daemon=True)
            remote_sync_thread.start()
            print("遠端資料庫同步已啟動")
        
        # 啟動控制信號監聽線程
        control_thread = threading.Thread(target=self._control_signal_worker, daemon=True)
        control_thread.start()
        
        print("資料庫記錄器已啟動")
    
    def _ssid_table_worker(self):
        """SSID_TABLE 更新工作線程"""
        while self.running:
            try:
                # 從隊列獲取數據
                sensor_data = self.data_manager.get_db_data(timeout=1.0)
                
                if sensor_data:
                    self._update_ssid_table(sensor_data)
                    
            except Exception as e:
                print(f"SSID_TABLE 更新錯誤: {e}")
                time.sleep(1)
    
    def _update_ssid_table(self, sensor_data: SensorData):
        """更新 SSID_TABLE"""
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 更新當前值和標誌
            cursor.execute(
                'UPDATE SSID_TABLE SET CURRENT_VALUE=%s, FLAG=%s WHERE DEVICE=%s',
                (sensor_data.value, '1', sensor_data.device_type.value)
            )
            
            conn.commit()
            cursor.close()
            
            print(f"SSID_TABLE 已更新: {sensor_data.device_type.value} = {sensor_data.value}")
            
        except Exception as e:
            print(f"更新 SSID_TABLE 失敗: {e}")
            if conn:
                conn.rollback()
        finally:
            if conn:
                self._return_connection(conn)
    
    def _history_worker(self):
        """歷史數據記錄工作線程"""
        while self.running:
            try:
                # 處理 BUFFER_RDB 中的數據
                self._process_buffer_rdb()
                time.sleep(2)  # 每2秒處理一次
                
            except Exception as e:
                print(f"歷史數據處理錯誤: {e}")
                time.sleep(5)
    
    def _process_buffer_rdb(self):
        """處理 BUFFER_RDB 中的數據"""
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 獲取未處理的數據
            cursor.execute(
                "SELECT ID, RDB_TABLE, VALUE FROM BUFFER_RDB WHERE PROCESSED=0 LIMIT 10"
            )
            buffer_data = cursor.fetchall()
            
            for record in buffer_data:
                buffer_id, table_name, json_data = record
                
                try:
                    # 解析 JSON 數據
                    data_dict = json.loads(json_data)
                    
                    # 插入到對應的表
                    if table_name == 'HISTORY':
                        self._insert_history_record(cursor, data_dict)
                    
                    # 標記為已處理
                    cursor.execute(
                        "UPDATE BUFFER_RDB SET PROCESSED=1 WHERE ID=%s",
                        (buffer_id,)
                    )
                    
                except Exception as e:
                    print(f"處理緩衝數據失敗 (ID: {buffer_id}): {e}")
                    # 標記為錯誤
                    cursor.execute(
                        "UPDATE BUFFER_RDB SET PROCESSED=-1 WHERE ID=%s",
                        (buffer_id,)
                    )
            
            conn.commit()
            cursor.close()
            
        except Exception as e:
            print(f"處理 BUFFER_RDB 失敗: {e}")
            if conn:
                conn.rollback()
        finally:
            if conn:
                self._return_connection(conn)
    
    def _insert_history_record(self, cursor, data_dict: Dict[str, Any]):
        """插入歷史記錄"""
        # 構建插入語句
        columns = list(data_dict.keys())
        values = list(data_dict.values())
        placeholders = ', '.join(['%s'] * len(values))
        column_names = ', '.join(columns)
        
        sql = f"INSERT INTO HISTORY ({column_names}) VALUES ({placeholders})"
        cursor.execute(sql, values)
    
    def _heartbeat_worker(self):
        """心跳工作線程"""
        while self.running:
            self.data_manager.update_heartbeat('db_logger')
            time.sleep(config.getfloat('System', 'heartbeat_interval', 1.0))
    
    def _remote_sync_worker(self):
        """遠端資料庫同步工作線程 - 整合 FA_RDB_OP.py 功能"""
        while self.running:
            try:
                current_time = time.time()
                if current_time - self.last_sync_time >= self.remote_sync_interval:
                    self._sync_to_remote_database()
                    self.last_sync_time = current_time
                
                time.sleep(0.5)  # 檢查間隔
                
            except Exception as e:
                print(f"遠端同步線程錯誤: {e}")
                time.sleep(self.remote_sync_interval)
    
    def _sync_to_remote_database(self):
        """同步數據到遠端資料庫 - FA_RDB_OP.py 核心邏輯"""
        local_conn = None
        remote_conn = None
        
        try:
            # 獲取待同步的數據
            local_conn = self._get_connection()
            local_cursor = local_conn.cursor()
            
            local_cursor.execute("SELECT ID, RDB_TABLE, VALUE FROM BUFFER_RDB LIMIT 100")
            buffer_data = local_cursor.fetchall()
            
            if not buffer_data:
                return  # 沒有數據需要同步
            
            # 連接遠端資料庫
            remote_conn = pymysql.connect(**self.remote_config)
            remote_cursor = remote_conn.cursor()
            
            synced_count = 0
            failed_count = 0
            
            for record in buffer_data:
                record_id, table_name, json_value = record
                
                try:
                    # 解析 JSON 數據
                    data_dict = json.loads(json_value)
                    
                    # 構建 INSERT 語句
                    columns = list(data_dict.keys())
                    values = [str(data_dict[col]) for col in columns]
                    
                    column_str = ','.join(columns)
                    placeholder_str = ','.join(['%s'] * len(values))
                    
                    insert_sql = f"INSERT INTO {table_name} ({column_str}) VALUES ({placeholder_str})"
                    
                    # 執行插入
                    remote_cursor.execute(insert_sql, values)
                    
                    # 刪除本地已同步的記錄
                    local_cursor.execute('DELETE FROM BUFFER_RDB WHERE ID=%s', (record_id,))
                    
                    synced_count += 1
                    
                except Exception as e:
                    print(f"同步記錄 {record_id} 失敗: {e}")
                    failed_count += 1
                    continue
            
            # 提交所有變更
            remote_conn.commit()
            local_conn.commit()
            
            # 輸出同步結果
            timestamp = datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
            if synced_count > 0:
                print(f"[{timestamp}] [RDB_SYNC] 成功同步 {synced_count} 筆記錄")
            
            if failed_count > 0:
                print(f"[{timestamp}] [RDB_SYNC] {failed_count} 筆記錄同步失敗")
            
            remote_cursor.close()
            local_cursor.close()
            
        except Exception as e:
            timestamp = datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
            print(f"[{timestamp}] [RDB_SYNC] [FAIL] {e}")
            
            if local_conn:
                local_conn.rollback()
            if remote_conn:
                remote_conn.rollback()
                
        finally:
            if local_conn:
                self._return_connection(local_conn)
            if remote_conn:
                remote_conn.close()
    
    def _control_signal_worker(self):
        """控制信號監聽線程"""
        while self.running:
            try:
                control_signal = self.data_manager.get_control_signal(timeout=1.0)
                if control_signal:
                    signal_type = control_signal.get('signal')
                    if signal_type == 'SHUTDOWN':
                        print("資料庫記錄器收到關閉信號")
                        self.stop()
                        break
            except:
                pass

    def log_sensor_data(self, device_type: DeviceType, value: str, additional_data: Dict = None):
        """記錄感測器數據到緩衝表"""
        conn = None
        try:
            # 準備數據
            log_data = {
                'DEVICE': device_type.value,
                'VALUE': value,
                'TIMESTAMP': time.strftime('%Y-%m-%d %H:%M:%S'),
                'SOURCE': 'SENSOR'
            }
            
            if additional_data:
                log_data.update(additional_data)
            
            json_data = json.dumps(log_data)
            
            # 插入到緩衝表
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                'INSERT INTO BUFFER_RDB (RDB_TABLE, VALUE, PROCESSED) VALUES (%s, %s, %s)',
                ('SENSOR_LOG', json_data, 0)
            )
            
            conn.commit()
            cursor.close()
            
        except Exception as e:
            print(f"記錄感測器數據失敗: {e}")
            if conn:
                conn.rollback()
        finally:
            if conn:
                self._return_connection(conn)
    
    def initialize_ssid_table(self):
        """初始化 SSID_TABLE"""
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 重置所有設備狀態
            devices = ['SCAN', 'RFID', 'SCREWDRIVER', 'WORK', 'POWER', 'JUMP', 'VOLTAGE']
            
            for device in devices:
                cursor.execute(
                    'UPDATE SSID_TABLE SET CURRENT_VALUE=%s, FLAG=%s WHERE DEVICE=%s',
                    ('', '0', device)
                )
            
            conn.commit()
            cursor.close()
            
            print("SSID_TABLE 初始化完成")
            
        except Exception as e:
            print(f"SSID_TABLE 初始化失敗: {e}")
            if conn:
                conn.rollback()
        finally:
            if conn:
                self._return_connection(conn)
    
    def stop(self):
        """停止資料庫記錄器"""
        print("正在停止資料庫記錄器...")
        self.running = False
        
        # 關閉連接池
        for conn in self.connection_pool:
            try:
                conn.close()
            except:
                pass
        
        self.data_manager.system_status['db_logger_active'] = False
        print("資料庫記錄器已停止")

if __name__ == "__main__":
    logger = DatabaseLogger()
    
    try:
        # 初始化資料表
        logger.initialize_ssid_table()
        
        # 啟動記錄器
        logger.start()
        
        # 保持運行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到停止信號...")
        logger.stop()
