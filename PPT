import requests
from pptx import Presentation
from pptx.util import Inches
from PIL import Image
from io import BytesIO
import os

# Unsplash API 設置 (需自行申請 API 金鑰: https://unsplash.com/developers)
UNSPLASH_API_URL = "https://api.unsplash.com/photos/random"
UNSPLASH_ACCESS_KEY = "你的API金鑰" # <-- 將此更改為你的 Unsplash API 金鑰

def fetch_image_from_unsplash(query):
    """
    從 Unsplash 搜尋隨機圖片。
    :param query: 搜尋關鍵字
    :return: 圖片的二進制數據
    """
    headers = {"Authorization": f"Client-ID {UNSPLASH_ACCESS_KEY}"}
    params = {"query": query, "orientation": "landscape", "count": 1}
    
    try:
        response = requests.get(UNSPLASH_API_URL, headers=headers, params=params)
        response.raise_for_status()  # 檢查 HTTP 錯誤
        image_url = response.json()[0]['urls']['regular']
        
        img_response = requests.get(image_url)
        img_response.raise_for_status()
        
        return img_response.content  # 回傳圖片的二進制數據
    except Exception as e:
        print(f"❌ 未能獲取圖片: {e}")
        return None

def generate_ppt(slides_content):
    """
    根據提供的文字內容生成 PPT。
    :param slides_content: 每張幻燈片的主題內容列表
    """
    try:
        # 創建 PPT 物件
        prs = Presentation()
        
        for slide_text in slides_content:
            slide = prs.slides.add_slide(prs.slide_layouts[5])  # 空白模板
            title = slide.shapes.title
            title.text = slide_text
            
            # 獲取與文字相關的圖片
            image_data = fetch_image_from_unsplash(slide_text)
            
            # 如果有圖片，插入到幻燈片中
            if image_data:
                image_path = f"{slide_text}.jpg"  # 臨時保存圖片
                with open(image_path, "wb") as f:
                    f.write(image_data)
                
                # 插入圖片到幻燈片
                left = Inches(1)
                top = Inches(1.5)
                height = Inches(3)
                slide.shapes.add_picture(image_path, left, top, height=height)
                
                # 刪除臨時圖片
                os.remove(image_path)
        
        # 保存 PPT
        ppt_path = "output_presentation.pptx"
        prs.save(ppt_path)
        print(f"✅ PPT 已生成: {ppt_path}")
    except Exception as e:
        print(f"❌ 生成 PPT 時發生錯誤: {e}")

def main():
    """
    主程式入口
    """
    print("📢 歡迎使用文字生成 PPT 工具!")
    topics = input("請輸入每張幻燈片的主題，用逗號分隔: ")
    slides_content = [topic.strip() for topic in topics.split(",") if topic.strip()]
    
    if not slides_content:
        print("❌ 請輸入至少一個主題。")
        return
    
    generate_ppt(slides_content)

if __name__ == "__main__":
    main()
