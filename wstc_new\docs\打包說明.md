# WSTC 感測器系統 - EXE 打包說明

## 概述

本專案已配置支援打包成 Windows 可執行檔 (.exe)，使用 PyInstaller 進行打包。

## 打包前準備

### 1. 安裝依賴
```bash
pip install -r requirements.txt
```

### 2. 確保所有檔案完整
- `main.py` - 主程式
- `config.ini` - 配置檔案
- 所有相關的 Python 模組檔案

## 打包方法

### 方法一：使用批次檔案（推薦）
1. 雙擊 `build_exe.bat`
2. 等待打包完成
3. 在 `dist` 目錄中找到可執行檔

### 方法二：使用 Python 腳本
```bash
python build_exe.py
```

### 方法三：手動使用 PyInstaller
```bash
pyinstaller --onefile --windowed --name=WSTC_Sensor_System --add-data=config.ini;. main.py
```

## 打包結果

打包完成後，在 `dist` 目錄中會生成：

- `WSTC_Sensor_System.exe` - 主可執行檔
- `啟動系統.bat` - 啟動批次檔案
- `config.ini` - 配置檔案

## 部署說明

### 單機部署
1. 將整個 `dist` 目錄複製到目標機器
2. 雙擊 `啟動系統.bat` 或 `WSTC_Sensor_System.exe`
3. 系統將自動啟動所有組件

### 網路部署
1. 將 `dist` 目錄複製到網路共享位置
2. 在目標機器建立捷徑指向可執行檔
3. 確保目標機器有適當的權限

## 技術細節

### freeze_support() 說明
在 `main.py` 中已添加 `mp.freeze_support()`，這是 PyInstaller 打包多進程應用程式的必要設定：

```python
if __name__ == "__main__":
    # 支援凍結打包 (PyInstaller, cx_Freeze 等)
    mp.freeze_support()
    
    # 設置多進程啟動方法
    mp.set_start_method('spawn', force=True)
    main()
```

### 打包參數說明
- `--onefile`: 打包成單一可執行檔
- `--windowed`: 不顯示控制台視窗
- `--add-data`: 包含額外的資料檔案
- `--hidden-import`: 包含隱藏的模組依賴

## 故障排除

### 常見問題

1. **打包失敗**
   - 檢查是否安裝了所有依賴
   - 確保 Python 版本相容
   - 檢查防毒軟體是否阻擋

2. **執行時錯誤**
   - 確保 `config.ini` 檔案存在
   - 檢查串口設備連接
   - 確認資料庫配置正確

3. **檔案過大**
   - 使用 `--onedir` 替代 `--onefile`
   - 排除不必要的模組

### 優化建議

1. **減少檔案大小**
   ```bash
   pyinstaller --onefile --windowed --exclude-module=matplotlib --exclude-module=numpy main.py
   ```

2. **提高啟動速度**
   ```bash
   pyinstaller --onedir --windowed main.py
   ```

3. **添加圖示**
   ```bash
   pyinstaller --onefile --windowed --icon=icon.ico main.py
   ```

## 版本控制

每次程式碼更新後，建議：
1. 更新版本號
2. 重新打包 EXE
3. 測試功能完整性
4. 更新部署檔案

## 聯絡支援

如有問題，請檢查：
1. Python 版本 (建議 3.8+)
2. 依賴套件版本
3. 系統權限設定
4. 防毒軟體設定 