"""
打包腳本 - 將專案打包成 EXE 可執行檔
使用 PyInstaller 進行打包
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """安裝 PyInstaller"""
    print("正在安裝 PyInstaller...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✓ PyInstaller 安裝成功")
    except subprocess.CalledProcessError as e:
        print(f"✗ PyInstaller 安裝失敗: {e}")
        return False
    return True

def build_exe():
    """打包 EXE 檔案"""
    print("開始打包 EXE 檔案...")
    
    # 清理舊的打包檔案
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    if os.path.exists("main.spec"):
        os.remove("main.spec")
    
    # PyInstaller 命令參數
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成單一檔案
        "--windowed",                   # 不顯示控制台視窗
        "--name=WSTC_Sensor_System",    # 可執行檔名稱
        "--icon=icon.ico",              # 圖示 (如果存在)
        "--add-data=config.ini;.",      # 包含配置檔案
        "--hidden-import=multiprocessing",
        "--hidden-import=tkinter",
        "--hidden-import=serial",
        "--hidden-import=sqlite3",
        "--hidden-import=threading",
        "--hidden-import=queue",
        "--hidden-import=time",
        "--hidden-import=signal",
        "--hidden-import=sys",
        "--hidden-import=os",
        "main.py"
    ]
    
    # 如果沒有圖示檔案，移除圖示參數
    if not os.path.exists("icon.ico"):
        cmd.remove("--icon=icon.ico")
    
    try:
        print("執行打包命令...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 打包成功完成")
        
        # 檢查生成的檔案
        exe_path = Path("dist/WSTC_Sensor_System.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"✓ 可執行檔已生成: {exe_path}")
            print(f"✓ 檔案大小: {size_mb:.1f} MB")
            
            # 複製配置檔案到 dist 目錄
            if os.path.exists("config.ini"):
                shutil.copy2("config.ini", "dist/")
                print("✓ 配置檔案已複製到 dist 目錄")
            
            return True
        else:
            print("✗ 可執行檔生成失敗")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"✗ 打包失敗: {e}")
        print(f"錯誤輸出: {e.stderr}")
        return False

def create_batch_file():
    """創建批次檔案用於啟動系統"""
    batch_content = """@echo off
title WSTC 感測器系統
echo 正在啟動 WSTC 感測器系統...
echo.

REM 檢查可執行檔是否存在
if not exist "WSTC_Sensor_System.exe" (
    echo 錯誤: 找不到 WSTC_Sensor_System.exe
    echo 請確保此批次檔案與可執行檔在同一目錄
    pause
    exit /b 1
)

REM 啟動系統
echo 啟動中...
start "" "WSTC_Sensor_System.exe"

echo 系統已啟動！
echo 按任意鍵關閉此視窗...
pause >nul
"""
    
    batch_path = Path("dist/啟動系統.bat")
    with open(batch_path, "w", encoding="utf-8") as f:
        f.write(batch_content)
    print(f"✓ 批次檔案已創建: {batch_path}")

def main():
    """主函數"""
    print("="*50)
    print("WSTC 感測器系統 - EXE 打包工具")
    print("="*50)
    
    # 檢查是否在正確的目錄
    if not os.path.exists("main.py"):
        print("錯誤: 請在專案根目錄執行此腳本")
        return
    
    # 安裝 PyInstaller
    if not install_pyinstaller():
        return
    
    # 打包 EXE
    if build_exe():
        # 創建批次檔案
        create_batch_file()
        
        print("\n" + "="*50)
        print("打包完成！")
        print("="*50)
        print("可執行檔位置: dist/WSTC_Sensor_System.exe")
        print("啟動批次檔: dist/啟動系統.bat")
        print("配置檔案: dist/config.ini")
        print("\n使用說明:")
        print("1. 將整個 dist 目錄複製到目標機器")
        print("2. 雙擊 '啟動系統.bat' 或 'WSTC_Sensor_System.exe'")
        print("3. 系統將自動啟動所有組件")
    else:
        print("\n打包失敗，請檢查錯誤訊息")

if __name__ == "__main__":
    main() 