import asyncio
import os, sys
import pymysql
import numpy as np
import serial
import binascii
import struct
import time
import threading
import configparser
from pynput import keyboard
import queue

# 跨平台鍵盤輸入處理
class CrossPlatformKeyboardHandler:
    def __init__(self):
        self.input_buffer = ""
        self.input_queue = queue.Queue()
        self.listener = None
        
    def on_press(self, key):
        try:
            # 處理普通字符
            if hasattr(key, 'char') and key.char is not None:
                self.input_buffer += key.char
        except AttributeError:
            # 處理特殊鍵
            if key == keyboard.Key.enter:
                # Enter 鍵被按下，處理輸入
                self.process_input()
            elif key == keyboard.Key.backspace:
                # 退格鍵
                if self.input_buffer:
                    self.input_buffer = self.input_buffer[:-1]
    
    def process_input(self):
        if self.input_buffer:
            # 將輸入放入佇列中處理
            self.input_queue.put(self.input_buffer)
            print(f'輸入接收: {self.input_buffer}')
            self.input_buffer = ""
    
    def start_listening(self):
        """啟動鍵盤監聽"""
        self.listener = keyboard.Listener(on_press=self.on_press)
        self.listener.start()
        
    def stop_listening(self):
        """停止鍵盤監聽"""
        if self.listener:
            self.listener.stop()
    
    def get_input(self):
        """獲取輸入（非阻塞）"""
        try:
            return self.input_queue.get_nowait()
        except queue.Empty:
            return None

config = configparser.ConfigParser()
config.read('config.ini')

local_host = config['LocalDatabase']['local_host']
local_user = config['LocalDatabase']['local_user']
local_password = config['LocalDatabase']['local_password']
local_database = config['LocalDatabase']['local_database']

def get_db_connection(host, user, password, database):
    return pymysql.connect(host=host, port=3306, user=user, passwd=password, db=database, charset='utf8')

# 全域鍵盤處理器
keyboard_handler = CrossPlatformKeyboardHandler()

def process_keyboard_input():
    """處理鍵盤輸入的線程"""
    while True:
        try:
            buf = keyboard_handler.get_input()
            if buf:
                scrw_l = len(buf)
                
                if scrw_l == 4:
                    # 螺絲刀相關處理
                    local_conn = get_db_connection(local_host, local_user, local_password, local_database)
                    local_cursor = local_conn.cursor()
                    local_cursor.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s', (buf, 'SCREWDRIVER'))
                    local_cursor.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s', ('1', 'SCREWDRIVER'))
                    local_conn.commit()
                    local_cursor.close()
                    local_conn.close()
                    
                elif scrw_l == 12:
                    # 工作相關處理
                    local_conn = get_db_connection(local_host, local_user, local_password, local_database)
                    local_cursor = local_conn.cursor()
                    local_cursor.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s', (buf, 'WORK'))
                    local_cursor.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s', ('1', 'WORK'))
                    local_conn.commit()
                    local_cursor.close()
                    local_conn.close()
                    
                print('處理輸入:', buf, scrw_l)
                
            time.sleep(0.1)  # 避免 CPU 過度使用
            
        except Exception as e:
            print(f"鍵盤輸入處理錯誤: {e}")
            time.sleep(1)

def get_T_form_sensor():
    # Windows COM port - 請根據實際情況調整 COM port 號碼
    try:
        ser1 = serial.Serial(port='COM1', baudrate=115200, timeout=0.5)
    except:
        print("無法連接到 COM1，請檢查串口設定")
        return
        
    while True:
        try:
            ser1.reset_input_buffer()
            ser1.reset_output_buffer()
            data = ser1.readline()
            
            data_ = binascii.b2a_hex(data)
            
            if data_ != b'':
                print(data_)
                result = str(data_)
                data1 = result[-9:-1]
                if data1 == 'cffcccff':
                    hex_data = data.hex().upper()
                    formatted_hex_data = ' '.join([hex_data[i:i + 2] for i in range(0, len(hex_data), 2)])
                    tdata = formatted_hex_data.replace("\n", " ").split()
                    # 每個數據段的長度
                    segment_length = 12
                    # 拆出變量部分的值
                    variable_values = []

                    for i in range(3, len(tdata), segment_length):
                        variable_value = (tdata[i] + tdata[i + 1] + tdata[i + 2] + tdata[i + 3] + tdata[i + 4])
                        variable_values.append(int(variable_value, 16)/10)

                    power = max(variable_values)

                    print('Torque:', power)
                    db = get_db_connection(local_host, 'power', '1q2w3e4r', local_database)
                    cur = db.cursor()
                    cur.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s', (power, 'POWER'))
                    cur.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s', ('1', 'POWER'))
                    db.commit()
                    cur.close()
                    db.close()
                        
        except Exception as e:
            print(f"扭力感測器錯誤: {e}")

def get_R_V_form_sensor():
    # Windows COM port - 請根據實際情況調整 COM port 號碼  
    try:
        ser4 = serial.Serial(port='COM4', baudrate=9600, timeout=1)
    except:
        print("無法連接到 COM4，請檢查串口設定")
        return
        
    r = bytes.fromhex('03 04 00 01 00 01 61 E8')  # 阻抗
    v = bytes.fromhex('03 04 00 03 00 01 C0 28')
    
    while True:
        try:
            ser4.write(v)
            time.sleep(1)
            data = ser4.readall()   
            if data != b'': 
                va = binascii.hexlify(data)
                s = str(va, encoding="utf-8")
                a = (((int(s[8:10], 16) - int('0x33', 16)) * 0.064) + 6.14)
                b = (a - 6.2) * 45.87
                b = round(b, 2)
                
                db = get_db_connection(local_host, 'power', '1q2w3e4r', local_database)
                cur = db.cursor()
                cur.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s', (b, 'VOLTAGE'))
                db.commit()
                cur.close()
                db.close()
                
        except Exception as e:
            print(f"電壓感測器錯誤: {e}")

def get_rfid_form_sensor():
    # Windows COM port - 請根據實際情況調整 COM port 號碼
    try:
        ser3 = serial.Serial(port='COM2', baudrate=9600, timeout=1)
    except:
        print("無法連接到 COM2，請檢查串口設定")
        return
        
    rfid = bytes.fromhex('55 00 02 05 00 00 05')
    
    while True:
        try:
            ser3.write(rfid)     
            data = ser3.readline()
            if data != b'':
                rfid_v = str(data, 'utf-8').replace('\r\n', '').replace(' ', '')
                
                if rfid_v != '0000000000':
                    db = get_db_connection(local_host, 'power', '1q2w3e4r', local_database)
                    cur = db.cursor()
                    cur.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s', (rfid_v, 'RFID'))
                    cur.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s', ('1', 'RFID'))
                    db.commit()
                    cur.close()
                    db.close()
                    print('RFID:', rfid_v)

        except Exception as e:
            print(f"RFID 感測器錯誤: {e}")

if __name__ == "__main__":
    print("啟動跨平台感測器系統...")
    
    # 啟動鍵盤監聽
    keyboard_handler.start_listening()
    print("鍵盤監聽已啟動")
    
    # 建立並啟動線程
    threads = []
    
    # 鍵盤輸入處理線程
    keyboard_thread = threading.Thread(target=process_keyboard_input, daemon=True)
    keyboard_thread.start()
    threads.append(keyboard_thread)
    
    # RFID 線程
    rfid_thread = threading.Thread(target=get_rfid_form_sensor, daemon=True)
    rfid_thread.start()
    threads.append(rfid_thread)
    
    # 電壓線程
    voltage_thread = threading.Thread(target=get_R_V_form_sensor, daemon=True)
    voltage_thread.start()
    threads.append(voltage_thread)
    
    # 扭力線程
    torque_thread = threading.Thread(target=get_T_form_sensor, daemon=True)
    torque_thread.start()
    threads.append(torque_thread)
    
    print("所有感測器線程已啟動")
    
    try:
        # 保持主程式運行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在關閉系統...")
        keyboard_handler.stop_listening()
        print("系統已關閉")