"""
UI設計器示範程式
展示如何使用設計器創建的UI
"""
import sys
import os

# 添加當前目錄到Python路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QLabel, QPushButton,
    QLineEdit, QTextEdit, QComboBox, QCheckBox, QSpinBox,
    QSlider, QProgressBar, QVBoxLayout, QHBoxLayout,
    QMessageBox
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 導入系統模組
from shared_data import get_data_manager, DeviceType, SensorData
from config import config

class DemoUI(QMainWindow):
    """示範UI類別 - 模擬UI設計器生成的程式碼"""
    
    def __init__(self):
        super().__init__()
        self.data_manager = get_data_manager()
        self.init_ui()
        self.connect_signals()
        
        # 模擬數據更新計時器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_data)
        self.update_timer.start(1000)  # 每秒更新一次
    
    def init_ui(self):
        """初始化UI - 模擬設計器生成的UI"""
        # 創建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 設定窗口標題和大小
        self.setWindowTitle("WSTC UI設計器 - 示範程式")
        self.setGeometry(100, 100, 600, 500)
        
        # 主佈局
        main_layout = QVBoxLayout()
        
        # 標題區域
        title_layout = QHBoxLayout()
        self.title_label = QLabel("WSTC系統監控面板")
        self.title_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(self.title_label)
        
        # 感測器數據顯示區域
        sensor_layout = QHBoxLayout()
        
        # 掃碼槍數據
        scan_group = QVBoxLayout()
        self.scan_label = QLabel("掃碼槍:")
        self.scan_value = QLineEdit()
        self.scan_value.setPlaceholderText("等待掃碼數據...")
        self.scan_value.setReadOnly(True)
        scan_group.addWidget(self.scan_label)
        scan_group.addWidget(self.scan_value)
        
        # RFID數據
        rfid_group = QVBoxLayout()
        self.rfid_label = QLabel("RFID:")
        self.rfid_value = QLineEdit()
        self.rfid_value.setPlaceholderText("等待RFID數據...")
        self.rfid_value.setReadOnly(True)
        rfid_group.addWidget(self.rfid_label)
        rfid_group.addWidget(self.rfid_value)
        
        sensor_layout.addLayout(scan_group)
        sensor_layout.addLayout(rfid_group)
        
        # 扭力值顯示
        power_layout = QHBoxLayout()
        self.power_label = QLabel("扭力值:")
        self.power_slider = QSlider(Qt.Horizontal)
        self.power_slider.setRange(0, 100)
        self.power_slider.setValue(0)
        self.power_display = QLabel("0")
        power_layout.addWidget(self.power_label)
        power_layout.addWidget(self.power_slider)
        power_layout.addWidget(self.power_display)
        
        # 狀態選擇
        status_layout = QHBoxLayout()
        self.status_label = QLabel("工作狀態:")
        self.status_combo = QComboBox()
        self.status_combo.addItems(["待機", "運行中", "暫停", "錯誤"])
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.status_combo)
        
        # 控制按鈕
        button_layout = QHBoxLayout()
        self.start_button = QPushButton("開始監控")
        self.stop_button = QPushButton("停止監控")
        self.clear_button = QPushButton("清除數據")
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        button_layout.addWidget(self.clear_button)
        
        # 日誌顯示
        self.log_label = QLabel("系統日誌:")
        self.log_display = QTextEdit()
        self.log_display.setMaximumHeight(150)
        self.log_display.setPlaceholderText("系統日誌將在此顯示...")
        
        # 狀態指示
        status_indicator_layout = QHBoxLayout()
        self.connection_check = QCheckBox("資料庫連線")
        self.sensor_check = QCheckBox("感測器連線")
        self.system_check = QCheckBox("系統運行")
        status_indicator_layout.addWidget(self.connection_check)
        status_indicator_layout.addWidget(self.sensor_check)
        status_indicator_layout.addWidget(self.system_check)
        
        # 添加所有佈局到主佈局
        main_layout.addLayout(title_layout)
        main_layout.addLayout(sensor_layout)
        main_layout.addLayout(power_layout)
        main_layout.addLayout(status_layout)
        main_layout.addLayout(button_layout)
        main_layout.addWidget(self.log_label)
        main_layout.addWidget(self.log_display)
        main_layout.addLayout(status_indicator_layout)
        
        central_widget.setLayout(main_layout)
        
        # 初始化日誌
        self.add_log("UI設計器示範程式已啟動")
        self.add_log("此UI是由WSTC UI設計器生成的示範")
    
    def connect_signals(self):
        """連接信號到槽函數"""
        self.start_button.clicked.connect(self.start_monitoring)
        self.stop_button.clicked.connect(self.stop_monitoring)
        self.clear_button.clicked.connect(self.clear_data)
        self.status_combo.currentTextChanged.connect(self.status_changed)
        self.power_slider.valueChanged.connect(self.power_changed)
    
    def start_monitoring(self):
        """開始監控"""
        self.add_log("開始監控感測器數據...")
        self.system_check.setChecked(True)
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        QMessageBox.information(self, "提示", "監控已開始")
    
    def stop_monitoring(self):
        """停止監控"""
        self.add_log("停止監控")
        self.system_check.setChecked(False)
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        QMessageBox.information(self, "提示", "監控已停止")
    
    def clear_data(self):
        """清除數據"""
        self.scan_value.clear()
        self.rfid_value.clear()
        self.power_slider.setValue(0)
        self.log_display.clear()
        self.add_log("數據已清除")
    
    def status_changed(self, status):
        """狀態改變"""
        self.add_log(f"工作狀態改變為: {status}")
    
    def power_changed(self, value):
        """扭力值改變"""
        self.power_display.setText(str(value))
        if value > 80:
            self.power_display.setStyleSheet("color: red; font-weight: bold;")
        elif value > 50:
            self.power_display.setStyleSheet("color: orange; font-weight: bold;")
        else:
            self.power_display.setStyleSheet("color: green;")
    
    def update_data(self):
        """更新數據顯示 - 整合到系統數據流"""
        try:
            # 檢查系統狀態
            if hasattr(self.data_manager, 'system_status'):
                db_active = self.data_manager.system_status.get('db_logger_active', False)
                sensor_active = self.data_manager.system_status.get('sensor_manager_active', False)
                
                self.connection_check.setChecked(db_active)
                self.sensor_check.setChecked(sensor_active)
            
            # 模擬從隊列獲取感測器數據
            if hasattr(self.data_manager, 'sensor_queue'):
                try:
                    while not self.data_manager.sensor_queue.empty():
                        sensor_data = self.data_manager.sensor_queue.get_nowait()
                        self.process_sensor_data(sensor_data)
                except:
                    pass  # 隊列為空
            
        except Exception as e:
            self.add_log(f"數據更新錯誤: {e}")
    
    def process_sensor_data(self, sensor_data: SensorData):
        """處理感測器數據"""
        device_type = sensor_data.device_type
        value = sensor_data.value
        
        if device_type == DeviceType.SCAN:
            self.scan_value.setText(value)
            self.add_log(f"掃碼數據: {value}")
        
        elif device_type == DeviceType.RFID:
            self.rfid_value.setText(value)
            self.add_log(f"RFID數據: {value}")
        
        elif device_type == DeviceType.POWER:
            try:
                power_value = int(float(value))
                self.power_slider.setValue(power_value)
                self.add_log(f"扭力值: {power_value}")
            except:
                pass
        
        elif device_type == DeviceType.LOG:
            self.add_log(f"系統: {value}")
    
    def add_log(self, message: str):
        """添加日誌"""
        import datetime
        timestamp = datetime.datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {message}"
        self.log_display.append(log_entry)
        
        # 限制日誌行數
        if self.log_display.document().blockCount() > 100:
            cursor = self.log_display.textCursor()
            cursor.movePosition(cursor.Start)
            cursor.select(cursor.BlockUnderCursor)
            cursor.removeSelectedText()

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設定應用程式屬性
    app.setApplicationName("WSTC UI設計器示範")
    app.setApplicationVersion("1.0")
    
    try:
        # 創建示範UI
        demo_ui = DemoUI()
        demo_ui.show()
        
        print("🎨 UI設計器示範程式已啟動")
        print("📱 此UI展示了設計器生成的程式碼結構")
        print("🔄 包含完整的系統整合功能")
        print("✅ 支援即時數據流更新")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 示範程式啟動失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()