"""
共享數據結構和隊列管理
高效的進程間通信核心模組
"""
import multiprocessing as mp
import threading
import time
from dataclasses import dataclass
from typing import Dict, Any, Optional
from enum import Enum

class DeviceType(Enum):
    """設備類型枚舉"""
    SCAN = "SCAN"              # 掃碼槍
    RFID = "RFID"              # RFID讀取器
    POWER = "POWER"            # 扭力感測器
    VOLTAGE = "VOLTAGE"        # 電壓/阻抗感測器
    SCREWDRIVER = "SCREWDRIVER" # 螺絲刀QR碼
    WORK = "WORK"              # 工單
    JUMP = "JUMP"              # 跳站
    LOG = "LOG"                # 日誌訊息

@dataclass
class SensorData:
    """感測器數據結構"""
    device_type: DeviceType
    value: str
    timestamp: float
    sequence: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'device_type': self.device_type.value,
            'value': self.value,
            'timestamp': self.timestamp,
            'sequence': self.sequence
        }

class DataManager:
    """數據管理器 - 負責進程間通信"""
    
    def __init__(self):
        # 主要數據隊列（感測器 → UI）
        self.sensor_queue = mp.Queue(maxsize=100)
        
        # 資料庫記錄隊列（背景記錄）
        self.db_queue = mp.Queue(maxsize=500)
        
        # 控制隊列（UI → 感測器）
        self.control_queue = mp.Queue(maxsize=50)
        
        # 共享狀態
        self.manager = mp.Manager()
        self.system_status = self.manager.dict({
            'sensor_active': False,
            'ui_active': False,
            'ui_ready': False,
            'db_logger_active': False,
            'db_sync_active': False,
            'last_heartbeat': time.time(),
            'shutdown_requested': False
        })

        # 心跳記錄
        self.heartbeats = self.manager.dict({
            'sensor_manager': time.time(),
            'ui': time.time(),
            'db_logger': time.time(),
            'db_sync': time.time()
        })

        # 最新數據快取（用於UI快速訪問）
        self.latest_data = self.manager.dict()
        
        self._sequence_counter = 0
        self._lock = threading.Lock()
    
    def get_next_sequence(self) -> int:
        """獲取下一個序列號"""
        with self._lock:
            self._sequence_counter += 1
            return self._sequence_counter
    
    def send_sensor_data(self, device_type: DeviceType, value: str) -> bool:
        """發送感測器數據"""
        try:
            data = SensorData(
                device_type=device_type,
                value=value,
                timestamp=time.time(),
                sequence=self.get_next_sequence()
            )
            
            # 發送到UI隊列（非阻塞）
            if not self.sensor_queue.full():
                self.sensor_queue.put_nowait(data)
            
            # 發送到資料庫隊列（非阻塞）
            if not self.db_queue.full():
                self.db_queue.put_nowait(data)
            
            # 更新最新數據快取
            self.latest_data[device_type.value] = data.to_dict()
            
            return True
            
        except Exception as e:
            print(f"發送數據失敗: {e}")
            return False
    
    def get_sensor_data(self, timeout: float = 0.1) -> Optional[SensorData]:
        """獲取感測器數據（用於UI）"""
        try:
            return self.sensor_queue.get(timeout=timeout)
        except:
            return None
    
    def get_db_data(self, timeout: float = 1.0) -> Optional[SensorData]:
        """獲取資料庫記錄數據"""
        try:
            return self.db_queue.get(timeout=timeout)
        except:
            return None
    
    def send_control_signal(self, signal: str):
        """發送控制信號"""
        try:
            control_data = {
                'signal': signal,
                'timestamp': time.time()
            }
            self.control_queue.put(control_data, timeout=1.0)
        except:
            pass

    def get_control_signal(self, timeout: float = 0.1):
        """獲取控制信號"""
        try:
            return self.control_queue.get(timeout=timeout)
        except:
            return None
    
    def update_heartbeat(self, component: str):
        """更新心跳"""
        self.heartbeats[component] = time.time()
    
    def is_system_healthy(self) -> bool:
        """檢查系統健康狀態"""
        try:
            current_time = time.time()
            
            # 檢查各組件心跳（允許更長的超時時間）
            sensor_heartbeat = self.heartbeats.get('sensor_manager', 0)
            ui_heartbeat = self.heartbeats.get('ui', 0)
            db_heartbeat = self.heartbeats.get('db_logger', 0)
            
            heartbeat_timeout = 10.0  # 增加超時時間到10秒
            
            # 只檢查活躍組件的心跳
            if self.system_status.get('sensor_active', False):
                if current_time - sensor_heartbeat > heartbeat_timeout:
                    return False
                
            if self.system_status.get('ui_active', False):
                if current_time - ui_heartbeat > heartbeat_timeout:
                    return False
                
            if self.system_status.get('db_logger_active', False):
                if current_time - db_heartbeat > heartbeat_timeout:
                    return False
            
            # 檢查隊列是否過載
            try:
                if self.sensor_queue.qsize() > 80:  # 隊列接近滿載
                    print("警告: 感測器隊列堆積")
                    return False
            except:
                pass
            
            return True
        
        except Exception as e:
            print(f"健康檢查錯誤: {e}")
            return False

# 全域數據管理器實例
data_manager = None

def get_data_manager() -> DataManager:
    """獲取全域數據管理器"""
    global data_manager
    if data_manager is None:
        data_manager = DataManager()
    return data_manager

# 只在主進程中初始化
if __name__ == '__main__':
    data_manager = DataManager()
else:
    data_manager = None
