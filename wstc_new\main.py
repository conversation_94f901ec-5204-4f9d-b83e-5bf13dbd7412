"""
主啟動程式 - 統一管理所有組件
高效能實時感測器系統
"""
import multiprocessing as mp
import time
import signal
import sys
import os

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from shared_data import get_data_manager
from config import config

def run_sensor_manager():
    """運行感測器管理器進程"""
    try:
        from sensor_manager import SensorManager
        
        print("啟動感測器管理器進程...")
        manager = SensorManager()
        manager.start()
        
        # 保持運行直到收到停止信號
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("感測器管理器收到停止信號")
    except Exception as e:
        print(f"感測器管理器錯誤: {e}")
    finally:
        if 'manager' in locals():
            manager.stop()

def run_database_logger():
    """運行資料庫記錄器進程"""
    try:
        from database_logger import DatabaseLogger
        
        print("啟動資料庫記錄器進程...")
        logger = DatabaseLogger()
        logger.initialize_ssid_table()  # 初始化資料表
        logger.start()
        
        # 保持運行直到收到停止信號
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("資料庫記錄器收到停止信號")
    except Exception as e:
        print(f"資料庫記錄器錯誤: {e}")
    finally:
        if 'logger' in locals():
            logger.stop()

def run_main_ui():
    """運行主UI進程"""
    try:
        from main_ui import main

        print("啟動主UI進程...")
        main()

    except KeyboardInterrupt:
        print("主UI收到停止信號")
    except Exception as e:
        print(f"主UI錯誤: {e}")

class SystemManager:
    """系統管理器"""
    
    def __init__(self):
        self.processes = []
        self.data_manager = get_data_manager()
        self.running = False
    
    def start(self):
        """啟動所有系統組件 - UI 優先啟動"""
        print("="*60)
        print("高效能實時感測器系統啟動中...")
        print("="*60)

        self.running = True

        try:
            # 1. 首先啟動主UI進程
            print("正在啟動 UI 界面...")
            ui_process = mp.Process(
                target=run_main_ui,
                name="MainUI"
            )
            ui_process.start()
            self.processes.append(ui_process)
            print("✓ 主UI進程已啟動")

            # 等待 UI 準備完成
            print("等待 UI 界面初始化...")
            ui_ready_timeout = 10  # 10秒超時
            start_time = time.time()
            while not self.data_manager.system_status.get('ui_ready', False):
                if time.time() - start_time > ui_ready_timeout:
                    print("警告: UI 初始化超時，繼續啟動其他組件")
                    break
                time.sleep(0.1)

            # 2. 啟動感測器管理器進程
            print("正在啟動感測器管理器...")
            sensor_process = mp.Process(
                target=run_sensor_manager,
                name="SensorManager"
            )
            sensor_process.start()
            self.processes.append(sensor_process)
            print("✓ 感測器管理器進程已啟動")

            # 等待一秒讓感測器初始化
            time.sleep(1)

            # 3. 啟動資料庫記錄器進程
            print("正在啟動資料庫記錄器...")
            db_process = mp.Process(
                target=run_database_logger,
                name="DatabaseLogger"
            )
            db_process.start()
            self.processes.append(db_process)
            print("✓ 資料庫記錄器進程已啟動")

            print("="*60)
            print("後台組件啟動完成！")
            print("請查看 UI 界面獲取詳細啟動狀態")
            print("="*60)

            # 監控所有進程
            self.monitor_processes()

        except Exception as e:
            print(f"系統啟動失敗: {e}")
            self.stop()
    
    def monitor_processes(self):
        """監控所有進程"""
        dead_processes = set()  # 記錄已死亡的進程，避免重複警告
        
        try:
            while self.running:
                # 檢查是否收到關閉信號
                control_signal = self.data_manager.get_control_signal(timeout=1.0)
                if control_signal and control_signal.get('signal') == 'SHUTDOWN':
                    print("收到UI關閉信號，正在停止系統...")
                    self.stop()
                    break
                
                # 檢查系統狀態中的關閉請求
                if self.data_manager.system_status.get('shutdown_requested', False):
                    print("檢測到關閉請求，正在停止系統...")
                    self.stop()
                    break
                
                # 檢查所有進程是否正常運行（避免重複警告）
                current_dead = set()
                for process in self.processes:
                    if not process.is_alive():
                        current_dead.add(process.name)
                        if process.name not in dead_processes:
                            print(f"警告: 進程 {process.name} 已停止")
                            dead_processes.add(process.name)
                
                # 如果有進程死亡，啟動關閉程序
                if current_dead and len(current_dead) >= len(self.processes):
                    print("所有子進程已停止，正在關閉系統...")
                    self.stop()
                    break
                elif current_dead:
                    # 部分進程死亡，等待一段時間後關閉
                    print(f"檢測到 {len(current_dead)} 個進程異常退出，將在3秒後關閉系統...")
                    time.sleep(3)
                    self.stop()
                    break
                
                # 只在有活躍進程時檢查系統健康狀態
                if len(current_dead) == 0:
                    try:
                        if not self.data_manager.is_system_healthy():
                            print("警告: 系統組件通信異常")
                    except:
                        # 如果無法檢查健康狀態，可能是數據管理器已關閉
                        pass
                
                time.sleep(1)  # 每秒檢查一次
                
        except KeyboardInterrupt:
            print("\n收到中斷信號，正在關閉系統...")
            self.stop()
        except Exception as e:
            print(f"監控進程時發生錯誤: {e}")
            self.stop()
    
    def stop(self):
        """停止所有系統組件"""
        if not self.running:
            return  # 避免重複執行
        
        print("正在停止系統...")
        self.running = False
        
        # 設置關閉標誌
        try:
            self.data_manager.system_status['shutdown_requested'] = True
        except:
            pass
        
        # 優雅停止所有進程
        for process in self.processes[:]:  # 使用副本避免修改原列表
            if process.is_alive():
                print(f"正在停止進程: {process.name}")
                try:
                    process.terminate()
                    process.join(timeout=3)  # 等待3秒
                    
                    if process.is_alive():
                        print(f"強制終止進程: {process.name}")
                        process.kill()
                        process.join(timeout=1)
                    
                    print(f"✓ 進程 {process.name} 已停止")
                    
                except Exception as e:
                    print(f"停止進程 {process.name} 時發生錯誤: {e}")
        
        print("系統已完全停止")
        
        # 清理資源
        try:
            self.data_manager = None
        except:
            pass
        
        # 確保程序完全退出
        import os
        os._exit(0)
    
    def signal_handler(self, signum, frame):
        """信號處理器"""
        print(f"\n收到信號 {signum}，正在關閉系統...")
        self.stop()
        sys.exit(0)

def main():
    """主函數"""
    # 設置信號處理
    manager = SystemManager()
    signal.signal(signal.SIGINT, manager.signal_handler)
    signal.signal(signal.SIGTERM, manager.signal_handler)
    
    try:
        # 檢查配置
        print("檢查系統配置...")
        db_config = config.get_db_config()
        serial_config = config.get_serial_config()
        
        print(f"資料庫: {db_config['host']}:{db_config['database']}")
        print(f"串口配置: {len(serial_config)} 個設備")
        
        # 啟動系統
        manager.start()
        
    except KeyboardInterrupt:
        print("\n用戶中斷")
    except Exception as e:
        print(f"系統錯誤: {e}")
    finally:
        manager.stop()

if __name__ == "__main__":
    # 支援凍結打包 (PyInstaller, cx_Freeze 等)
    mp.freeze_support()

    # 設置多進程啟動方法 - Windows 兼容性改進
    try:
        # 嘗試使用 spawn 方法
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        # 如果已經設置過，忽略錯誤
        pass
    except Exception as e:
        print(f"設置多進程方法失敗: {e}")
        print("使用默認多進程方法")

    main()
