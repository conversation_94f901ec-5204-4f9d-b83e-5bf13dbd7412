#!/bin/bash

# 高效能實時感測器系統啟動腳本
# 用於樹莓派環境的一鍵啟動

echo "=========================================="
echo "啟動高效能實時感測器系統"
echo "=========================================="

# 檢查 Python 版本
python_version=$(python3 --version 2>&1)
echo "Python 版本: $python_version"

# 檢查依賴
echo "檢查系統依賴..."

# 檢查 MySQL 服務
if systemctl is-active --quiet mysql; then
    echo "✓ MySQL 服務正常運行"
else
    echo "✗ MySQL 服務未運行，嘗試啟動..."
    sudo systemctl start mysql
    if systemctl is-active --quiet mysql; then
        echo "✓ MySQL 服務啟動成功"
    else
        echo "✗ MySQL 服務啟動失敗，請檢查配置"
        exit 1
    fi
fi

# 檢查串口設備
echo "檢查串口設備..."
if [ -e "/dev/ttyAMA0" ]; then
    echo "✓ 扭力感測器串口 /dev/ttyAMA0"
else
    echo "⚠ 扭力感測器串口 /dev/ttyAMA0 未找到"
fi

if [ -e "/dev/ttyAMA2" ]; then
    echo "✓ RFID感測器串口 /dev/ttyAMA2"
else
    echo "⚠ RFID感測器串口 /dev/ttyAMA2 未找到"
fi

if [ -e "/dev/ttyAMA4" ]; then
    echo "✓ 電壓感測器串口 /dev/ttyAMA4"
else
    echo "⚠ 電壓感測器串口 /dev/ttyAMA4 未找到"
fi

# 檢查網路連接
echo "檢查網路連接..."
if ping -c 1 8.8.8.8 &> /dev/null; then
    echo "✓ 網路連接正常"
else
    echo "⚠ 網路連接異常"
fi

# 設置環境變數
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 創建日誌目錄
mkdir -p logs

# 啟動系統
echo "=========================================="
echo "正在啟動系統..."
echo "=========================================="

# 選項1：前台運行（推薦用於除錯）
if [ "$1" = "debug" ]; then
    echo "除錯模式：前台運行"
    python3 main.py
else
    # 選項2：背景運行
    echo "生產模式：背景運行"
    nohup python3 main.py > logs/system.log 2>&1 &
    system_pid=$!
    echo "系統已在背景啟動，PID: $system_pid"
    echo "查看日誌: tail -f logs/system.log"
    echo "停止系統: kill $system_pid"
    
    # 保存 PID 到文件
    echo $system_pid > logs/system.pid
fi

echo "=========================================="
echo "系統啟動完成！"
echo "=========================================="