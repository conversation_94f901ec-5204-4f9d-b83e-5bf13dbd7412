import asyncio, evdev
import os, sys
import pymysql
import numpy as np
import serial
import binascii
import struct
import time
import threading
import configparser

# 確保 evdev 模組已安裝
# pip install evdev

# 按键转字符表 只列出了常用的字符
keymap = {'KEY_0': u'0', 'KEY_1': u'1', 'KEY_2': u'2', 'KEY_3': u'3',
          'KEY_4': u'4', 'KEY_5': u'5', 'KEY_6': u'6', 'KEY_7': u'7', 'KEY_8':
              u'8', 'KEY_9': u'9', 'KEY_A': u'A', 'KEY_B': u'B', 'KEY_C': u'C',
          'KEY_D': u'D', 'KEY_E': u'E', 'KEY_F': u'F', 'KEY_G': u'G', 'KEY_H':
              u'H', 'KEY_I': u'I', 'KEY_J': u'J', 'KEY_K': u'K', 'KEY_L': u'L',
          'KEY_M': u'M', 'KEY_N': u'N', 'KEY_O': u'O', 'KEY_P': u'P', 'KEY_Q':
              u'Q', 'KEY_R': u'R', 'KEY_S': u'S', 'KEY_T': u'T', 'KEY_U': u'U',
          'KEY_V': u'V', 'KEY_W': u'W', 'KEY_X': u'X', 'KEY_Y': u'Y', 'KEY_Z':
              u'Z', 'KEY_TAB': u'\t', 'KEY_SPACE': u' ', 'KEY_COMMA': u',',
          'KEY_SEMICOLON': u';', 'KEY_EQUAL': u'=', 'KEY_LEFTBRACE': u'[',
          'KEY_RIGHTBRACE': u']', 'KEY_MINUS': u'-', 'KEY_APOSTROPHE': u'\'',
          'KEY_GRAVE': u'`', 'KEY_DOT': u'.', 'KEY_SLASH': u'/',
          'KEY_BACKSLASH': u'\\', 'KEY_ENTER': u'\n', 'KEY_NUMLOCK': u'\n',
          'KEY_KPMINUS': u'-', 'KEY_KP1': u'1', 'KEY_KP0': u'0', 'KEY_KP2': u'2',
          'KEY_KP3': u'3', 'KEY_KP4': u'4', 'KEY_KP5': u'5', 'KEY_KP6': u'6',
          'KEY_KP7': u'7', 'KEY_KP8': u'8', 'KEY_KP9': u'9', 'KEY_KPDOT': u'.',
          'KEY_LEFTSHIFT': u''
          }

config = configparser.ConfigParser()
config.read('config.ini')

local_host = config['LocalDatabase']['local_host']
local_user = config['LocalDatabase']['local_user']
local_password = config['LocalDatabase']['local_password']
local_database = config['LocalDatabase']['local_database']

def get_db_connection(host, user, password, database):
    return pymysql.connect(host=host, port=3306, user=user, passwd=password, db=database, charset='utf8')

# 检测到输入时触发
async def print_events(device):
    buf = ''
    async for event in device.async_read_loop():
                
        if event.type == evdev.ecodes.EV_KEY and event.value == 1:
            kv = evdev.events.KeyEvent(event)
            # 本次修改的地方, 把事件映射到字符表
            if kv.scancode == evdev.ecodes.KEY_ENTER:
                lgh = len(device.name)
                print(lgh)
                time.sleep(0.5)
                if lgh == 13:
                    local_conn = get_db_connection(local_host, local_user, local_password, local_database)
                    local_cursor = local_conn.cursor()
                    scrw_l = len(buf)

                    if scrw_l == 4:

                        local_cursor.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s',(buf, 'SCREWDRIVER'))
                        local_cursor.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s', ('1', 'SCREWDRIVER'))
                    elif scrw_l == 12:
                        local_cursor.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s', (buf, 'WORK'))
                        local_cursor.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s', ('1', 'WORK'))

                    local_conn.commit()
                    local_cursor.close()
                    local_conn.close()

                print('读到输入: ', buf, lgh)
                ''' 业务逻辑 '''
                # 清空 buffer
                buf = ''
                lgh = ''
            else:

                buf += keymap.get(kv.keycode, '')
                #print(buf)
        

devices = [evdev.InputDevice(path) for path in evdev.list_devices()]
print('发现以下设备: ')

for device in devices:
    print(device.path, device.name, device.phys)
    print(len(device.name))

for device in devices:
    asyncio.ensure_future(print_events(device))

def get_T_form_sensor():
    # Windows COM port - 請根據實際情況調整 COM port 號碼
    ser1 = serial.Serial(port='COM1', baudrate=115200,timeout=0.5)#115200
    while True:
        try:
            
            ser1.reset_input_buffer()
            ser1.reset_output_buffer()
            data = ser1.readline()
            
            data_=binascii.b2a_hex(data)
            
        #             
            if data_ != b'':
                print(data_)
                result = str(data_)
                data1 = result[-9:-1]
                if data1 == 'cffcccff':
                    hex_data = data.hex().upper()
                    formatted_hex_data = ' '.join([hex_data[i:i + 2] for i in range(0, len(hex_data), 2)])
                    tdata = formatted_hex_data.replace("\n", " ").split()
                    # 每個數據段的長度
                    segment_length = 12
                    # 拆出變量部分的值
                    variable_values = []

                    for i in range(3, len(tdata), segment_length):
                        variable_value = (tdata[i] + tdata[i + 1] + tdata[i + 2] + tdata[i + 3]+ tdata[i + 4])
                        variable_values.append(int(variable_value, 16)/10)

                    power=max(variable_values)

                    print('Torque :',power)
                    db = get_db_connection(local_host, 'power', '1q2w3e4r', local_database)
                    cur = db.cursor()
                    cur.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s',(power,'POWER'))
                    cur.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('1','POWER'))
                    db.commit()
                    cur.close()
                    db.close()
                        
        except:
            print("power has wrong")
#             db = pymysql.connect(host=local_host,port = 3306,user='power',passwd='1q2w3e4r',db = local_database ,charset = 'utf8')
#             cur = db.cursor()
#             cur.execute('SELECT CURRENT_VALUE FROM SSID_TABLE WHERE DEVICE=%s','POWER')
#             torque = cur.fetchall()
#             torque = round(float(torque[0][0]) - float('0.01'),2)
#             
#             cur.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s',(torque,'POWER'))
#             cur.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('1','POWER'))
#             db.commit()
#             db.close()
#             cur.close()

def get_R_V_form_sensor():
    # Windows COM port - 請根據實際情況調整 COM port 號碼  
    ser4 = serial.Serial(port='COM4', baudrate=9600,timeout=1)
    r    = bytes.fromhex('03 04 00 01 00 01 61 E8')#阻抗
    v    = bytes.fromhex('03 04 00 03 00 01 C0 28')
    while True:
        #try:
#         ser4.write(r)
#         data = ser4.readall()
#         print('3',data)
        ser4.write(v)
        time.sleep(1)
        data = ser4.readall()   
        if  data != b'': 
             
            va= binascii.hexlify(data)
            s=str(va, encoding = "utf-8")
            a= (((int(s[8:10],16) -int('0x33',16)) *0.064)+6.14)
            #123
            #q123
            #print(a)
            b =(a-6.2)*45.87
            b=round(b,2)
            #print(b)
            db = get_db_connection(local_host, 'power', '1q2w3e4r', local_database)
            cur = db.cursor()
            cur.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s',(b,'VOLTAGE'))
            db.commit()
            cur.close()
            db.close()
                
                
#                 if b <= 25:
#                     db = pymysql.connect(host=local_host,port = 3306,user='power',passwd='1q2w3e4r',db = local_database)
#                     cur = db.cursor()
#                     #cur.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s',(a,'VOLTAGE'))
#                     cur.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('1','VOLTAGE'))
#                     db.commit()
#                     db.close()
#                     cur.close()

#             elif  a >= 7.5 :
#                 db = pymysql.connect(host=local_host,port = 3306,user='power',passwd='1q2w3e4r',db = local_database)
#                 cur = db.cursor()
#                 #cur.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s',(a,'VOLTAGE'))
#                 cur.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('2','VOLTAGE'))
#                 db.commit()
#                 db.close()
#                 cur.close()
# 
#         except:
#             print("v has wrong")
    

def get_rfid_form_sensor():
    # Windows COM port - 請根據實際情況調整 COM port 號碼
    ser3 = serial.Serial(port='COM2', baudrate=9600,timeout=1)
    rfid = bytes.fromhex('55 00 02 05 00 00 05')
    while True:
        try:
            ser3.write(rfid)     
            data = ser3.readline()
            if data != b'':
                
                rfid_v=str(data,'utf-8').replace('\r\n','').replace(' ','')
                
                if rfid_v != '0000000000':
                    db = get_db_connection(local_host, 'power', '1q2w3e4r', local_database)
                    cur = db.cursor()
                    cur.execute('UPDATE SSID_TABLE SET CURRENT_VALUE=%s WHERE DEVICE=%s',(rfid_v,'RFID'))
                    cur.execute('UPDATE SSID_TABLE SET FLAG=%s WHERE DEVICE=%s',('1','RFID'))
                    db.commit()
                    cur.close()
                    db.close()
                    print('RFID:',rfid_v)

        except:
            print("rfid has wrong")

RFID = threading.Thread(target=get_rfid_form_sensor)
R_V= threading.Thread(target=get_R_V_form_sensor)
T = threading.Thread(target=get_T_form_sensor)

RFID.start()
R_V.start()
T.start()


loop = asyncio.get_event_loop()
loop.run_forever()
