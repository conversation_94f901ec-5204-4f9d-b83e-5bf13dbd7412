"""
資料庫同步器 - 整合版本
負責從遠端資料庫同步 MAPPING 和 TEST_MEMBER 資料到本地資料庫
"""
import pymysql
import time
import datetime
import json
import traceback
import threading
import sys
import os

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import config
from shared_data import get_data_manager

class DatabaseSynchronizer:
    """資料庫同步器類"""
    
    def __init__(self):
        self.running = False
        self.data_manager = get_data_manager()

        # 從配置文件讀取設備序號
        try:
            device_config = config.get_device_config()
            self.box_id = device_config['box_id']
        except:
            self.box_id = '1'  # 默認值

        self.sync_interval = 2  # 同步間隔（秒）
        
        # 從配置文件獲取本地資料庫配置
        self.local_config = config.get_db_config()
        
        # 遠端資料庫配置（從本地資料庫的 _SYS 表讀取）
        self.remote_config = None
        
    def initialize(self):
        """初始化同步器，讀取遠端資料庫配置"""
        try:
            # 連接本地資料庫讀取遠端配置
            local_db = pymysql.connect(**self.local_config)
            cursor = local_db.cursor()
            
            sql = "SELECT _KEY, _VALUE FROM _SYS"
            cursor.execute(sql)
            config_data = cursor.fetchall()
            
            # 解析配置
            _config = {}
            for key, value in config_data:
                _config[key] = value
            
            # 設置遠端資料庫配置
            self.remote_config = {
                'host': _config.get('HOST_RDB', 'localhost'),
                'port': int(_config.get('PORT_RDB', 3306)),
                'user': _config.get('USER_RDB', 'root'),
                'password': _config.get('PASSWD_RDB', ''),
                'database': _config.get('BACKUP_DB', 'backup')
            }
            
            cursor.close()
            local_db.close()
            
            self._log_message("資料庫同步器初始化完成")
            return True
            
        except Exception as e:
            self._log_message(f"資料庫同步器初始化失敗: {e}")
            return False
    
    def start(self):
        """啟動同步器"""
        if not self.initialize():
            return False
        
        self.running = True
        
        # 啟動同步線程
        sync_thread = threading.Thread(target=self._sync_worker, daemon=True)
        sync_thread.start()
        
        # 更新系統狀態
        self.data_manager.system_status['db_sync_active'] = True
        
        self._log_message("資料庫同步器已啟動")
        return True
    
    def stop(self):
        """停止同步器"""
        self.running = False
        self.data_manager.system_status['db_sync_active'] = False
        self._log_message("資料庫同步器已停止")
    
    def _sync_worker(self):
        """同步工作線程"""
        while self.running:
            try:
                self._perform_sync()
                
                # 更新心跳
                self.data_manager.update_heartbeat('db_sync')
                
            except Exception as e:
                self._log_message(f"同步過程發生錯誤: {e}")
                self._log_message(f"錯誤詳情: {traceback.format_exc()}")
            
            # 等待下次同步
            time.sleep(self.sync_interval)
    
    def _perform_sync(self):
        """執行同步操作"""
        if not self.remote_config:
            return
        
        try:
            # 連接遠端資料庫
            remote_db = pymysql.connect(**self.remote_config)
            remote_cursor = remote_db.cursor()
            
            # 檢查 MAPPING 表更新標誌
            remote_cursor.execute(
                "SELECT FLAG FROM SSID_TABLE WHERE NAME=%s AND BOX_ID=%s",
                ('MAPPING', self.box_id)
            )
            mapping_flag = remote_cursor.fetchone()
            
            # 檢查 TEST_MEMBER 表更新標誌
            remote_cursor.execute(
                "SELECT FLAG FROM SSID_TABLE WHERE NAME=%s AND BOX_ID=%s",
                ('TEST_MEMBER', self.box_id)
            )
            member_flag = remote_cursor.fetchone()
            
            # 同步 MAPPING 表
            if mapping_flag and mapping_flag[0] == '1':
                self._sync_mapping_table(remote_cursor)
            
            # 同步 TEST_MEMBER 表
            if member_flag and member_flag[0] == '1':
                self._sync_member_table(remote_cursor)
            
            remote_cursor.close()
            remote_db.close()
            
        except Exception as e:
            self._log_message(f"同步操作失敗: {e}")
            raise
    
    def _sync_mapping_table(self, remote_cursor):
        """同步 MAPPING 表"""
        try:
            # 從遠端獲取 MAPPING 資料
            sql_get = """
                SELECT BARCODE, SITE, PLANT, CUSTOMER, MACHINE_MODEL, 
                       STATION, POWER_MIN, POWER_MAX 
                FROM MAPPING
            """
            remote_cursor.execute(sql_get)
            mapping_data = remote_cursor.fetchall()
            
            # 連接本地資料庫
            local_db = pymysql.connect(**self.local_config)
            local_cursor = local_db.cursor()
            
            # 清空本地 INFO 表
            local_cursor.execute("TRUNCATE TABLE INFO")
            local_db.commit()
            
            # 插入新資料
            sql_put = """
                INSERT INTO INFO(BARCODE, SITE, PLANT, CUSTOMER, MACHINE_MODEL, 
                                STATION, POWER_MIN, POWER_MAX) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            local_cursor.executemany(sql_put, list(mapping_data))
            local_db.commit()
            
            local_cursor.close()
            local_db.close()
            
            # 更新遠端標誌
            remote_cursor.execute(
                'UPDATE SSID_TABLE SET FLAG=%s WHERE NAME=%s AND BOX_ID=%s',
                ('0', 'MAPPING', self.box_id)
            )
            
            self._log_message(f"MAPPING 表同步完成，更新 {len(mapping_data)} 筆記錄")
            
        except Exception as e:
            self._log_message(f"MAPPING 表同步失敗: {e}")
            raise
    
    def _sync_member_table(self, remote_cursor):
        """同步 TEST_MEMBER 表"""
        try:
            # 從遠端獲取 TEST_MEMBER 資料
            sql_get = """
                SELECT DEPARTMENT, JOB_NUMBER, NAME, RFID_NUMBER, PLANT, FUNCTION 
                FROM TEST_MEMBER
            """
            remote_cursor.execute(sql_get)
            member_data = remote_cursor.fetchall()
            
            # 連接本地資料庫
            local_db = pymysql.connect(**self.local_config)
            local_cursor = local_db.cursor()
            
            # 清空本地 TEST_MEMBER 表
            local_cursor.execute("TRUNCATE TABLE TEST_MEMBER")
            local_db.commit()
            
            # 插入新資料
            sql_put = """
                INSERT INTO TEST_MEMBER(DEPARTMENT, JOB_NUMBER, NAME, RFID_NUMBER, PLANT, FUNCTION) 
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            local_cursor.executemany(sql_put, list(member_data))
            local_db.commit()
            
            local_cursor.close()
            local_db.close()
            
            # 更新遠端標誌
            remote_cursor.execute(
                'UPDATE SSID_TABLE SET FLAG=%s WHERE NAME=%s AND BOX_ID=%s',
                ('0', 'TEST_MEMBER', self.box_id)
            )
            
            self._log_message(f"TEST_MEMBER 表同步完成，更新 {len(member_data)} 筆記錄")
            
        except Exception as e:
            self._log_message(f"TEST_MEMBER 表同步失敗: {e}")
            raise
    
    def _log_message(self, message):
        """記錄日誌訊息"""
        timestamp = datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        log_message = f"[{timestamp}] [DB_SYNC] {message}"
        print(log_message)
        
        # 如果數據管理器可用，也發送到 UI
        try:
            from shared_data import DeviceType
            self.data_manager.send_sensor_data(DeviceType.LOG, log_message)
        except:
            pass

def main():
    """獨立運行模式"""
    print("資料庫同步器 - 獨立運行模式")
    
    synchronizer = DatabaseSynchronizer()
    
    try:
        if synchronizer.start():
            print("同步器已啟動，按 Ctrl+C 停止...")
            while True:
                time.sleep(1)
        else:
            print("同步器啟動失敗")
    except KeyboardInterrupt:
        print("\n收到停止信號...")
    finally:
        synchronizer.stop()
        print("同步器已停止")

if __name__ == "__main__":
    main()
