#!/usr/bin/env python3
"""
配置管理工具
用於管理多台設備的配置文件
"""
import os
import shutil
import sys
from pathlib import Path

# 添加父目錄到 Python 路徑以導入配置模組
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        # 設置相對於項目根目錄的路徑
        project_root = Path(__file__).parent.parent
        self.config_dir = project_root / "configs"
        self.active_config = self.config_dir / "config.ini"
        
    def list_available_configs(self):
        """列出可用的配置文件"""
        configs = []
        for config_file in self.config_dir.glob("config_station*.ini"):
            station_num = config_file.stem.replace("config_station", "")
            configs.append({
                'file': config_file,
                'station': station_num,
                'name': f"工站{station_num}"
            })
        return sorted(configs, key=lambda x: int(x['station']))
    
    def get_current_station(self):
        """獲取當前配置的工站號"""
        try:
            from config import config
            device_config = config.get_device_config()
            return device_config['station_id']
        except:
            return None
    
    def switch_to_station(self, station_id):
        """切換到指定工站的配置"""
        source_config = self.config_dir / f"config_station{station_id}.ini"
        
        if not source_config.exists():
            raise FileNotFoundError(f"工站{station_id}的配置文件不存在: {source_config}")
        
        # 備份當前配置
        if self.active_config.exists():
            backup_config = self.config_dir / "config_backup.ini"
            shutil.copy2(self.active_config, backup_config)
            print(f"已備份當前配置到: {backup_config}")
        
        # 複製新配置
        shutil.copy2(source_config, self.active_config)
        print(f"已切換到工站{station_id}配置")
        
        return True
    
    def create_station_config(self, station_id, station_name=None, **kwargs):
        """創建新的工站配置"""
        if station_name is None:
            station_name = f"工站{station_id}"
        
        config_file = self.config_dir / f"config_station{station_id}.ini"
        
        # 基於默認配置創建
        template_config = self.config_dir / "config.ini"
        if template_config.exists():
            shutil.copy2(template_config, config_file)
        
        # 更新設備配置
        self._update_device_section(config_file, station_id, station_name, **kwargs)
        
        print(f"已創建工站{station_id}配置: {config_file}")
        return config_file
    
    def _update_device_section(self, config_file, station_id, station_name, **kwargs):
        """更新配置文件的設備區段"""
        import configparser
        
        config = configparser.ConfigParser()
        config.read(config_file, encoding='utf-8')
        
        # 更新 Device 區段
        if 'Device' not in config:
            config.add_section('Device')
        
        config.set('Device', 'station_id', str(station_id))
        config.set('Device', 'station_name', station_name)
        config.set('Device', 'box_id', str(station_id))
        
        # 更新其他配置
        for key, value in kwargs.items():
            if '.' in key:
                section, option = key.split('.', 1)
                if section not in config:
                    config.add_section(section)
                config.set(section, option, str(value))
        
        # 寫回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
    
    def show_status(self):
        """顯示當前配置狀態"""
        current_station = self.get_current_station()
        available_configs = self.list_available_configs()
        
        print("=" * 50)
        print("WSTC 配置管理器")
        print("=" * 50)
        
        if current_station:
            print(f"當前工站: #{current_station}")
        else:
            print("當前工站: 未知")
        
        print(f"活動配置: {self.active_config}")
        
        print("\n可用配置:")
        for config_info in available_configs:
            status = " (當前)" if config_info['station'] == str(current_station) else ""
            print(f"  工站{config_info['station']}: {config_info['file']}{status}")
        
        if not available_configs:
            print("  無可用的工站配置文件")

def main():
    """主函數"""
    manager = ConfigManager()
    
    if len(sys.argv) < 2:
        manager.show_status()
        print("\n使用方法:")
        print("  python config_manager.py status          # 顯示狀態")
        print("  python config_manager.py switch <工站號>  # 切換工站")
        print("  python config_manager.py create <工站號>  # 創建工站配置")
        print("  python config_manager.py list            # 列出可用配置")
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == "status":
            manager.show_status()
            
        elif command == "list":
            configs = manager.list_available_configs()
            print("可用的工站配置:")
            for config_info in configs:
                print(f"  工站{config_info['station']}: {config_info['name']}")
                
        elif command == "switch":
            if len(sys.argv) < 3:
                print("錯誤: 請指定工站號")
                return
            
            station_id = sys.argv[2]
            manager.switch_to_station(station_id)
            print("配置切換完成！請重新啟動系統以使配置生效。")
            
        elif command == "create":
            if len(sys.argv) < 3:
                print("錯誤: 請指定工站號")
                return
            
            station_id = sys.argv[2]
            station_name = sys.argv[3] if len(sys.argv) > 3 else None
            manager.create_station_config(station_id, station_name)
            
        else:
            print(f"未知命令: {command}")
            
    except Exception as e:
        print(f"錯誤: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
