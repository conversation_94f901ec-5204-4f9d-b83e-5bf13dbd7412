"""
UI設計器測試版本 - 簡化版
不依賴系統模組，用於測試基本功能
"""
import sys
import json
import os
from typing import Dict, Any, List, Optional

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QComboBox, QLineEdit, QCheckBox,
    QSpinBox, QSlider, QProgressBar, QTabWidget, QSplitter,
    QListWidget, QTreeWidget, QTableWidget, QScrollArea,
    QGroupBox, QFrame, QToolBox, QStackedWidget,
    QGraphicsDropShadowEffect,
    QDialog, QDialogButtonBox, QFormLayout, QGridLayout,
    QFileDialog, QMessageBox, QColorDialog, QFontDialog
)
from PyQt5.QtCore import (
    Qt, QMimeData, QTimer, pyqtSignal, QPropertyAnimation,
    QEasingCurve, QRect, QPoint, QSize
)
from PyQt5.QtGui import (
    QDrag, QPainter, QPixmap, QFont, QColor, QPalette,
    QDragEnterEvent, QDropEvent, QMouseEvent, QPaintEvent
)

class WidgetInfo:
    """控件資訊類別"""
    def __init__(self, widget_type: str, display_name: str, icon: str = ""):
        self.widget_type = widget_type
        self.display_name = display_name
        self.icon = icon
        self.default_properties = {}

class DraggableWidget(QLabel):
    """可拖拉的控件源"""
    
    def __init__(self, widget_info: WidgetInfo):
        super().__init__()
        self.widget_info = widget_info
        self.setText(widget_info.display_name)
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #3498db;
                border-radius: 5px;
                background-color: #ecf0f1;
                padding: 8px;
                margin: 2px;
            }
            QLabel:hover {
                background-color: #bdc3c7;
                border-color: #2980b9;
            }
        """)
        self.setMinimumSize(120, 40)
        
    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.pos()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        if not (event.buttons() & Qt.LeftButton):
            return
        
        if ((event.pos() - self.drag_start_position).manhattanLength() < 
            QApplication.startDragDistance()):
            return
        
        # 開始拖拉操作
        drag = QDrag(self)
        mimeData = QMimeData()
        mimeData.setText(self.widget_info.widget_type)
        drag.setMimeData(mimeData)
        
        # 創建拖拉時的預覽圖
        pixmap = QPixmap(self.size())
        self.render(pixmap)
        drag.setPixmap(pixmap)
        
        # 執行拖拉
        drop_action = drag.exec_(Qt.CopyAction)

class DesignCanvas(QWidget):
    """設計畫布"""
    
    widget_selected = pyqtSignal(object)  # 控件被選中信號
    
    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.setMinimumSize(800, 600)
        self.setStyleSheet("""
            DesignCanvas {
                background-color: white;
                border: 1px solid #bdc3c7;
            }
        """)
        
        # 已放置的控件
        self.placed_widgets = []
        self.selected_widget = None
        self.widget_counter = 0
        
        # 網格設定
        self.show_grid = True
        self.grid_size = 10
        
    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasText():
            event.accept()
        else:
            event.ignore()
    
    def dragMoveEvent(self, event):
        if event.mimeData().hasText():
            event.accept()
        else:
            event.ignore()
    
    def dropEvent(self, event: QDropEvent):
        widget_type = event.mimeData().text()
        position = event.pos()
        
        # 對齊到網格
        if self.show_grid:
            x = (position.x() // self.grid_size) * self.grid_size
            y = (position.y() // self.grid_size) * self.grid_size
            position = QPoint(x, y)
        
        # 創建控件
        widget = self.create_widget(widget_type, position)
        if widget:
            event.accept()
        else:
            event.ignore()
    
    def create_widget(self, widget_type: str, position: QPoint):
        """在指定位置創建控件"""
        self.widget_counter += 1
        widget_name = f"{widget_type}_{self.widget_counter}"
        
        # 根據類型創建控件
        widget = None
        default_size = QSize(100, 30)
        
        if widget_type == "QLabel":
            widget = QLabel("Label", self)
            widget.setAlignment(Qt.AlignCenter)
            default_size = QSize(80, 25)
            
        elif widget_type == "QPushButton":
            widget = QPushButton("Button", self)
            default_size = QSize(100, 30)
            
        elif widget_type == "QLineEdit":
            widget = QLineEdit(self)
            widget.setPlaceholderText("輸入文字...")
            default_size = QSize(150, 25)
            
        elif widget_type == "QTextEdit":
            widget = QTextEdit(self)
            widget.setPlaceholderText("多行文字...")
            default_size = QSize(200, 100)
            
        elif widget_type == "QComboBox":
            widget = QComboBox(self)
            widget.addItems(["選項1", "選項2", "選項3"])
            default_size = QSize(120, 25)
            
        elif widget_type == "QCheckBox":
            widget = QCheckBox("勾選框", self)
            default_size = QSize(80, 25)
            
        elif widget_type == "QSpinBox":
            widget = QSpinBox(self)
            widget.setRange(0, 100)
            default_size = QSize(80, 25)
            
        elif widget_type == "QSlider":
            widget = QSlider(Qt.Horizontal, self)
            widget.setRange(0, 100)
            default_size = QSize(150, 25)
            
        elif widget_type == "QProgressBar":
            widget = QProgressBar(self)
            widget.setValue(50)
            default_size = QSize(200, 25)
            
        if widget:
            # 設定控件屬性
            widget.setObjectName(widget_name)
            widget.move(position)
            widget.resize(default_size)
            widget.show()
            
            # 使控件可選中和移動
            self.make_widget_selectable(widget)
            
            # 加入到已放置控件列表
            widget_info = {
                'widget': widget,
                'type': widget_type,
                'name': widget_name,
                'position': position,
                'size': default_size
            }
            self.placed_widgets.append(widget_info)
            
            return widget
        
        return None
    
    def make_widget_selectable(self, widget):
        """使控件可選中和移動"""
        widget.mousePressEvent = lambda event: self.select_widget(widget, event)
        widget.mouseMoveEvent = lambda event: self.move_widget(widget, event)
        
        # 添加選中樣式
        original_style = widget.styleSheet()
        widget.setProperty('original_style', original_style)
    
    def select_widget(self, widget, event):
        """選中控件"""
        if self.selected_widget:
            # 取消之前選中的控件樣式
            old_style = self.selected_widget.property('original_style') or ""
            self.selected_widget.setStyleSheet(old_style)
        
        # 選中新控件
        self.selected_widget = widget
        current_style = widget.property('original_style') or ""
        selected_style = current_style + """
            border: 2px dashed #e74c3c;
            background-color: rgba(231, 76, 60, 0.1);
        """
        widget.setStyleSheet(selected_style)
        
        # 發送選中信號
        self.widget_selected.emit(widget)
        
        # 記錄拖拉起始位置
        if event.button() == Qt.LeftButton:
            self.drag_start_pos = event.globalPos() - widget.pos()
    
    def move_widget(self, widget, event):
        """移動控件"""
        if event.buttons() & Qt.LeftButton and widget == self.selected_widget:
            new_pos = event.globalPos() - self.drag_start_pos
            
            # 對齊到網格
            if self.show_grid:
                x = (new_pos.x() // self.grid_size) * self.grid_size
                y = (new_pos.y() // self.grid_size) * self.grid_size
                new_pos = QPoint(x, y)
            
            widget.move(new_pos)
            
            # 更新控件資訊
            for info in self.placed_widgets:
                if info['widget'] == widget:
                    info['position'] = new_pos
                    break
    
    def paintEvent(self, event: QPaintEvent):
        """繪製網格"""
        super().paintEvent(event)
        
        if self.show_grid:
            painter = QPainter(self)
            painter.setPen(QColor(200, 200, 200))
            
            # 繪製垂直線
            for x in range(0, self.width(), self.grid_size):
                painter.drawLine(x, 0, x, self.height())
            
            # 繪製水平線
            for y in range(0, self.height(), self.grid_size):
                painter.drawLine(0, y, self.width(), y)
    
    def delete_selected_widget(self):
        """刪除選中的控件"""
        if self.selected_widget:
            # 從列表中移除
            for info in self.placed_widgets:
                if info['widget'] == self.selected_widget:
                    self.placed_widgets.remove(info)
                    break
            
            # 刪除控件
            self.selected_widget.deleteLater()
            self.selected_widget = None
    
    def clear_canvas(self):
        """清空畫布"""
        for info in self.placed_widgets:
            info['widget'].deleteLater()
        self.placed_widgets.clear()
        self.selected_widget = None
        self.widget_counter = 0

class WidgetPalette(QWidget):
    """控件調色盤"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 標題
        title = QLabel("控件庫")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title)
        
        # 基本控件
        basic_group = QGroupBox("基本控件")
        basic_layout = QVBoxLayout()
        
        # 定義可用控件
        widgets = [
            WidgetInfo("QLabel", "標籤"),
            WidgetInfo("QPushButton", "按鈕"),
            WidgetInfo("QLineEdit", "單行輸入"),
            WidgetInfo("QTextEdit", "多行輸入"),
            WidgetInfo("QComboBox", "下拉選單"),
            WidgetInfo("QCheckBox", "勾選框"),
            WidgetInfo("QSpinBox", "數字框"),
            WidgetInfo("QSlider", "滑桿"),
            WidgetInfo("QProgressBar", "進度條")
        ]
        
        for widget_info in widgets:
            draggable = DraggableWidget(widget_info)
            basic_layout.addWidget(draggable)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # 空白填充
        layout.addStretch()
        
        self.setLayout(layout)

class TestUIDesigner(QMainWindow):
    """測試版UI設計器主視窗"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.current_file = None
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("UI設計器測試版 - WSTC系統")
        self.setGeometry(100, 100, 1200, 700)
        
        # 創建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主佈局
        main_layout = QHBoxLayout()
        
        # 左側控件庫
        self.palette = WidgetPalette()
        self.palette.setMaximumWidth(200)
        
        # 中央設計區域
        canvas_scroll = QScrollArea()
        self.canvas = DesignCanvas()
        canvas_scroll.setWidget(self.canvas)
        canvas_scroll.setWidgetResizable(True)
        
        # 添加到佈局
        main_layout.addWidget(self.palette)
        main_layout.addWidget(canvas_scroll, 1)
        
        central_widget.setLayout(main_layout)
        
        # 創建選單欄
        self.create_menu()
        
        # 創建狀態欄
        self.statusBar().showMessage("就緒 - 測試版")
    
    def create_menu(self):
        """創建選單欄"""
        menubar = self.menuBar()
        
        # 檔案選單
        file_menu = menubar.addMenu('檔案')
        file_menu.addAction('新建', self.new_design)
        file_menu.addAction('清空畫布', self.clear_canvas)
        file_menu.addSeparator()
        file_menu.addAction('退出', self.close)
        
        # 編輯選單
        edit_menu = menubar.addMenu('編輯')
        edit_menu.addAction('刪除', self.delete_selected)
        
        # 檢視選單
        view_menu = menubar.addMenu('檢視')
        view_menu.addAction('顯示網格', self.toggle_grid)
    
    def new_design(self):
        """新建設計"""
        self.canvas.clear_canvas()
        self.current_file = None
        self.statusBar().showMessage("新建設計")
    
    def delete_selected(self):
        """刪除選中的控件"""
        self.canvas.delete_selected_widget()
        self.statusBar().showMessage("已刪除選中控件")
    
    def clear_canvas(self):
        """清空畫布"""
        reply = QMessageBox.question(
            self, "確認", "確定要清空畫布嗎？",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.canvas.clear_canvas()
            self.statusBar().showMessage("已清空畫布")
    
    def toggle_grid(self):
        """切換網格顯示"""
        self.canvas.show_grid = not self.canvas.show_grid
        self.canvas.update()
        status = "顯示" if self.canvas.show_grid else "隱藏"
        self.statusBar().showMessage(f"網格已{status}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設定應用程式屬性
    app.setApplicationName("WSTC UI設計器測試版")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    designer = TestUIDesigner()
    designer.show()
    
    print("🎨 UI設計器測試版已啟動")
    print("✅ 基本拖拉功能正常")
    print("✅ 控件創建功能正常")
    print("✅ 網格對齊功能正常")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 