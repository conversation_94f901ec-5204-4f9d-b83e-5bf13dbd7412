# ===== 核心依賴 =====
pymysql>=1.0.2
pyserial>=3.5
requests>=2.28.0

# ===== 資料結構與類型支援 =====
# Python 3.6 相容性支援
dataclasses>=0.8; python_version < "3.7"
typing-extensions>=4.0.0

# ===== 樹莓派專用（Linux感測器） =====
evdev>=1.4.0; platform_machine == "armv7l" or platform_machine == "aarch64" or platform_system == "Linux"

# ===== 跨平台鍵盤監聽 =====
pynput>=1.7.6

# ===== GUI 框架 =====
PyQt5>=5.15.0
# UI設計器額外依賴
sip>=6.0.0

# ===== XML 解析（SOAP WebService） =====
# xml.dom.minidom - Python 內建
# 如果需要更好的 XML 支援可以添加：
# lxml>=4.6.0

# ===== 可選但推薦的依賴 =====
# 數值計算（如果需要進階數學運算）
numpy>=1.21.0

# 更好的配置文件支援
# configparser - Python 內建，但可以用更強的版本
# configobj>=5.0.6

# 日誌管理（如果需要進階日誌功能）
# loguru>=0.6.0

# 監控和調試
# psutil>=5.8.0  # 系統資源監控
# memory-profiler>=0.60.0  # 記憶體分析

# ===== 開發和測試依賴 =====
# pytest>=6.0.0  # 單元測試
# black>=22.0.0   # 程式碼格式化
# flake8>=4.0.0   # 程式碼檢查

# ===== 打包工具 =====
pyinstaller>=5.0.0  # EXE 打包工具