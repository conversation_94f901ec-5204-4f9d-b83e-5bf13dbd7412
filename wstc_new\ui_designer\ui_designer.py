"""
UI設計器 - 拖拉式UI建構工具
基於現有系統架構，支援視覺化UI設計
"""
import sys
import json
import os
from typing import Dict, Any, List, Optional

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QComboBox, QLineEdit, QCheckBox,
    QSpinBox, QSlider, QProgressBar, QTabWidget, QSplitter,
    QListWidget, QTreeWidget, QTableWidget, QScrollArea,
    QGroupBox, QFrame, QToolBox, QStackedWidget,
    QGraphicsDropShadowEffect,
    QDialog, QDialogButtonBox, QFormLayout, QGridLayout,
    QFileDialog, QMessageBox, QColorDialog, QFontDialog
)
from PyQt5.QtCore import (
    Qt, QMimeData, QTimer, pyqtSignal, QPropertyAnimation,
    QEasingCurve, QRect, QPoint, QSize
)
from PyQt5.QtGui import (
    QDrag, QPainter, QPixmap, QFont, QColor, QPalette,
    QDragEnterEvent, QDropEvent, QMouseEvent, QPaintEvent
)

# 導入現有系統模組
import sys
import os

# 添加父目錄到Python路徑以導入系統模組
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 父目錄 (wstc_new)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    from shared_data import get_data_manager, DeviceType, SensorData
    from config import config
except ImportError as e:
    print(f"❌ 無法導入系統模組: {e}")
    print("請確保從正確的目錄啟動UI設計器")
    sys.exit(1)

class WidgetInfo:
    """控件資訊類別"""
    def __init__(self, widget_type: str, display_name: str, icon: str = ""):
        self.widget_type = widget_type
        self.display_name = display_name
        self.icon = icon
        self.default_properties = {}

class DraggableWidget(QLabel):
    """可拖拉的控件源"""
    
    def __init__(self, widget_info: WidgetInfo):
        super().__init__()
        self.widget_info = widget_info
        self.setText(widget_info.display_name)
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #3498db;
                border-radius: 5px;
                background-color: #ecf0f1;
                padding: 8px;
                margin: 2px;
            }
            QLabel:hover {
                background-color: #bdc3c7;
                border-color: #2980b9;
            }
        """)
        self.setMinimumSize(120, 40)
        
    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.pos()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        if not (event.buttons() & Qt.LeftButton):
            return
        
        if ((event.pos() - self.drag_start_position).manhattanLength() < 
            QApplication.startDragDistance()):
            return
        
        # 開始拖拉操作
        drag = QDrag(self)
        mimeData = QMimeData()
        mimeData.setText(self.widget_info.widget_type)
        drag.setMimeData(mimeData)
        
        # 創建拖拉時的預覽圖
        pixmap = QPixmap(self.size())
        self.render(pixmap)
        drag.setPixmap(pixmap)
        
        # 執行拖拉
        drop_action = drag.exec_(Qt.CopyAction)

class DesignCanvas(QWidget):
    """設計畫布"""
    
    widget_selected = pyqtSignal(object)  # 控件被選中信號
    
    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.setMinimumSize(800, 600)
        self.setStyleSheet("""
            DesignCanvas {
                background-color: white;
                border: 1px solid #bdc3c7;
            }
        """)
        
        # 已放置的控件
        self.placed_widgets = []
        self.selected_widget = None
        self.widget_counter = 0
        
        # 網格設定
        self.show_grid = True
        self.grid_size = 10
        
    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasText():
            event.accept()
        else:
            event.ignore()
    
    def dragMoveEvent(self, event):
        if event.mimeData().hasText():
            event.accept()
        else:
            event.ignore()
    
    def dropEvent(self, event: QDropEvent):
        widget_type = event.mimeData().text()
        position = event.pos()
        
        # 對齊到網格
        if self.show_grid:
            x = (position.x() // self.grid_size) * self.grid_size
            y = (position.y() // self.grid_size) * self.grid_size
            position = QPoint(x, y)
        
        # 創建控件
        widget = self.create_widget(widget_type, position)
        if widget:
            event.accept()
        else:
            event.ignore()
    
    def create_widget(self, widget_type: str, position: QPoint):
        """在指定位置創建控件"""
        self.widget_counter += 1
        widget_name = f"{widget_type}_{self.widget_counter}"
        
        # 根據類型創建控件
        widget = None
        default_size = QSize(100, 30)
        
        if widget_type == "QLabel":
            widget = QLabel("Label", self)
            widget.setAlignment(Qt.AlignCenter)
            default_size = QSize(80, 25)
            
        elif widget_type == "QPushButton":
            widget = QPushButton("Button", self)
            default_size = QSize(100, 30)
            
        elif widget_type == "QLineEdit":
            widget = QLineEdit(self)
            widget.setPlaceholderText("輸入文字...")
            default_size = QSize(150, 25)
            
        elif widget_type == "QTextEdit":
            widget = QTextEdit(self)
            widget.setPlaceholderText("多行文字...")
            default_size = QSize(200, 100)
            
        elif widget_type == "QComboBox":
            widget = QComboBox(self)
            widget.addItems(["選項1", "選項2", "選項3"])
            default_size = QSize(120, 25)
            
        elif widget_type == "QCheckBox":
            widget = QCheckBox("勾選框", self)
            default_size = QSize(80, 25)
            
        elif widget_type == "QSpinBox":
            widget = QSpinBox(self)
            widget.setRange(0, 100)
            default_size = QSize(80, 25)
            
        elif widget_type == "QSlider":
            widget = QSlider(Qt.Horizontal, self)
            widget.setRange(0, 100)
            default_size = QSize(150, 25)
            
        elif widget_type == "QProgressBar":
            widget = QProgressBar(self)
            widget.setValue(50)
            default_size = QSize(200, 25)
            
        if widget:
            # 設定控件屬性
            widget.setObjectName(widget_name)
            widget.move(position)
            widget.resize(default_size)
            widget.show()
            
            # 使控件可選中和移動
            self.make_widget_selectable(widget)
            
            # 加入到已放置控件列表
            widget_info = {
                'widget': widget,
                'type': widget_type,
                'name': widget_name,
                'position': position,
                'size': default_size
            }
            self.placed_widgets.append(widget_info)
            
            return widget
        
        return None
    
    def make_widget_selectable(self, widget):
        """使控件可選中和移動"""
        widget.mousePressEvent = lambda event: self.select_widget(widget, event)
        widget.mouseMoveEvent = lambda event: self.move_widget(widget, event)
        
        # 添加選中樣式
        original_style = widget.styleSheet()
        widget.setProperty('original_style', original_style)
    
    def select_widget(self, widget, event):
        """選中控件"""
        if self.selected_widget:
            # 取消之前選中的控件樣式
            old_style = self.selected_widget.property('original_style') or ""
            self.selected_widget.setStyleSheet(old_style)
        
        # 選中新控件
        self.selected_widget = widget
        current_style = widget.property('original_style') or ""
        selected_style = current_style + """
            border: 2px dashed #e74c3c;
            background-color: rgba(231, 76, 60, 0.1);
        """
        widget.setStyleSheet(selected_style)
        
        # 發送選中信號
        self.widget_selected.emit(widget)
        
        # 記錄拖拉起始位置
        if event.button() == Qt.LeftButton:
            self.drag_start_pos = event.globalPos() - widget.pos()
    
    def move_widget(self, widget, event):
        """移動控件"""
        if event.buttons() & Qt.LeftButton and widget == self.selected_widget:
            new_pos = event.globalPos() - self.drag_start_pos
            
            # 對齊到網格
            if self.show_grid:
                x = (new_pos.x() // self.grid_size) * self.grid_size
                y = (new_pos.y() // self.grid_size) * self.grid_size
                new_pos = QPoint(x, y)
            
            widget.move(new_pos)
            
            # 更新控件資訊
            for info in self.placed_widgets:
                if info['widget'] == widget:
                    info['position'] = new_pos
                    break
    
    def paintEvent(self, event: QPaintEvent):
        """繪製網格"""
        super().paintEvent(event)
        
        if self.show_grid:
            painter = QPainter(self)
            painter.setPen(QColor(200, 200, 200))
            
            # 繪製垂直線
            for x in range(0, self.width(), self.grid_size):
                painter.drawLine(x, 0, x, self.height())
            
            # 繪製水平線
            for y in range(0, self.height(), self.grid_size):
                painter.drawLine(0, y, self.width(), y)
    
    def delete_selected_widget(self):
        """刪除選中的控件"""
        if self.selected_widget:
            # 從列表中移除
            for info in self.placed_widgets:
                if info['widget'] == self.selected_widget:
                    self.placed_widgets.remove(info)
                    break
            
            # 刪除控件
            self.selected_widget.deleteLater()
            self.selected_widget = None
    
    def clear_canvas(self):
        """清空畫布"""
        for info in self.placed_widgets:
            info['widget'].deleteLater()
        self.placed_widgets.clear()
        self.selected_widget = None
        self.widget_counter = 0

class PropertyEditor(QWidget):
    """屬性編輯器"""
    
    def __init__(self):
        super().__init__()
        self.current_widget = None
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        self.title_label = QLabel("屬性編輯器")
        self.title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(self.title_label)
        
        # 屬性表單
        self.property_form = QFormLayout()
        layout.addLayout(self.property_form)
        
        # 空白填充
        layout.addStretch()
        
        self.setLayout(layout)
    
    def show_properties(self, widget):
        """顯示控件屬性"""
        self.current_widget = widget
        
        # 清空現有屬性
        self.clear_properties()
        
        if not widget:
            return
        
        # 基本屬性
        self.add_text_property("物件名稱", widget.objectName())
        self.add_position_property("位置", widget.pos())
        self.add_size_property("大小", widget.size())
        
        # 根據控件類型顯示特定屬性
        widget_type = type(widget).__name__
        
        if widget_type in ["QLabel", "QPushButton"]:
            if hasattr(widget, 'text'):
                self.add_text_property("文字", widget.text())
        
        elif widget_type == "QLineEdit":
            if hasattr(widget, 'text'):
                self.add_text_property("文字", widget.text())
            if hasattr(widget, 'placeholderText'):
                self.add_text_property("提示文字", widget.placeholderText())
        
        elif widget_type == "QTextEdit":
            if hasattr(widget, 'toPlainText'):
                self.add_text_property("文字", widget.toPlainText())
        
        elif widget_type == "QCheckBox":
            if hasattr(widget, 'text'):
                self.add_text_property("文字", widget.text())
            if hasattr(widget, 'isChecked'):
                self.add_bool_property("選中", widget.isChecked())
        
        # 樣式屬性
        self.add_color_property("背景顏色")
        self.add_color_property("文字顏色")
        self.add_font_property("字體")
    
    def clear_properties(self):
        """清空屬性表單"""
        while self.property_form.count():
            child = self.property_form.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def add_text_property(self, name: str, value: str):
        """添加文字屬性"""
        edit = QLineEdit(value)
        edit.textChanged.connect(lambda text: self.update_widget_property(name, text))
        self.property_form.addRow(name + ":", edit)
    
    def add_position_property(self, name: str, pos: QPoint):
        """添加位置屬性"""
        widget = QWidget()
        layout = QHBoxLayout()
        
        x_edit = QSpinBox()
        x_edit.setRange(-9999, 9999)
        x_edit.setValue(pos.x())
        x_edit.valueChanged.connect(lambda: self.update_position())
        
        y_edit = QSpinBox()
        y_edit.setRange(-9999, 9999)
        y_edit.setValue(pos.y())
        y_edit.valueChanged.connect(lambda: self.update_position())
        
        layout.addWidget(QLabel("X:"))
        layout.addWidget(x_edit)
        layout.addWidget(QLabel("Y:"))
        layout.addWidget(y_edit)
        
        widget.setLayout(layout)
        self.property_form.addRow(name + ":", widget)
        
        # 儲存編輯器引用
        widget.x_edit = x_edit
        widget.y_edit = y_edit
        setattr(self, 'position_editor', widget)
    
    def add_size_property(self, name: str, size: QSize):
        """添加大小屬性"""
        widget = QWidget()
        layout = QHBoxLayout()
        
        w_edit = QSpinBox()
        w_edit.setRange(1, 9999)
        w_edit.setValue(size.width())
        w_edit.valueChanged.connect(lambda: self.update_size())
        
        h_edit = QSpinBox()
        h_edit.setRange(1, 9999)
        h_edit.setValue(size.height())
        h_edit.valueChanged.connect(lambda: self.update_size())
        
        layout.addWidget(QLabel("寬:"))
        layout.addWidget(w_edit)
        layout.addWidget(QLabel("高:"))
        layout.addWidget(h_edit)
        
        widget.setLayout(layout)
        self.property_form.addRow(name + ":", widget)
        
        # 儲存編輯器引用
        widget.w_edit = w_edit
        widget.h_edit = h_edit
        setattr(self, 'size_editor', widget)
    
    def add_bool_property(self, name: str, value: bool):
        """添加布林屬性"""
        checkbox = QCheckBox()
        checkbox.setChecked(value)
        checkbox.stateChanged.connect(lambda state: self.update_widget_property(name, state == Qt.Checked))
        self.property_form.addRow(name + ":", checkbox)
    
    def add_color_property(self, name: str):
        """添加顏色屬性"""
        button = QPushButton("選擇顏色")
        button.clicked.connect(lambda: self.choose_color(name))
        self.property_form.addRow(name + ":", button)
    
    def add_font_property(self, name: str):
        """添加字體屬性"""
        button = QPushButton("選擇字體")
        button.clicked.connect(lambda: self.choose_font())
        self.property_form.addRow(name + ":", button)
    
    def update_widget_property(self, property_name: str, value):
        """更新控件屬性"""
        if not self.current_widget:
            return
        
        if property_name == "物件名稱":
            self.current_widget.setObjectName(str(value))
        elif property_name == "文字":
            if hasattr(self.current_widget, 'setText'):
                self.current_widget.setText(str(value))
        elif property_name == "提示文字":
            if hasattr(self.current_widget, 'setPlaceholderText'):
                self.current_widget.setPlaceholderText(str(value))
        elif property_name == "選中":
            if hasattr(self.current_widget, 'setChecked'):
                self.current_widget.setChecked(bool(value))
    
    def update_position(self):
        """更新位置"""
        if self.current_widget and hasattr(self, 'position_editor'):
            x = self.position_editor.x_edit.value()
            y = self.position_editor.y_edit.value()
            self.current_widget.move(x, y)
    
    def update_size(self):
        """更新大小"""
        if self.current_widget and hasattr(self, 'size_editor'):
            w = self.size_editor.w_edit.value()
            h = self.size_editor.h_edit.value()
            self.current_widget.resize(w, h)
    
    def choose_color(self, property_name: str):
        """選擇顏色"""
        color = QColorDialog.getColor()
        if color.isValid():
            # 應用顏色到控件
            if property_name == "背景顏色":
                self.current_widget.setStyleSheet(f"background-color: {color.name()};")
            elif property_name == "文字顏色":
                self.current_widget.setStyleSheet(f"color: {color.name()};")
    
    def choose_font(self):
        """選擇字體"""
        font, ok = QFontDialog.getFont()
        if ok:
            self.current_widget.setFont(font)

class WidgetPalette(QWidget):
    """控件調色盤"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 標題
        title = QLabel("控件庫")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title)
        
        # 基本控件
        basic_group = QGroupBox("基本控件")
        basic_layout = QVBoxLayout()
        
        # 定義可用控件
        widgets = [
            WidgetInfo("QLabel", "標籤"),
            WidgetInfo("QPushButton", "按鈕"),
            WidgetInfo("QLineEdit", "單行輸入"),
            WidgetInfo("QTextEdit", "多行輸入"),
            WidgetInfo("QComboBox", "下拉選單"),
            WidgetInfo("QCheckBox", "勾選框"),
            WidgetInfo("QSpinBox", "數字框"),
            WidgetInfo("QSlider", "滑桿"),
            WidgetInfo("QProgressBar", "進度條")
        ]
        
        for widget_info in widgets:
            draggable = DraggableWidget(widget_info)
            basic_layout.addWidget(draggable)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # 容器控件
        container_group = QGroupBox("容器控件")
        container_layout = QVBoxLayout()
        
        container_widgets = [
            WidgetInfo("QGroupBox", "群組框"),
            WidgetInfo("QTabWidget", "標籤頁"),
            WidgetInfo("QFrame", "框架")
        ]
        
        for widget_info in container_widgets:
            draggable = DraggableWidget(widget_info)
            container_layout.addWidget(draggable)
        
        container_group.setLayout(container_layout)
        layout.addWidget(container_group)
        
        # 空白填充
        layout.addStretch()
        
        self.setLayout(layout)

class CodeGenerator:
    """程式碼生成器"""
    
    @staticmethod
    def generate_ui_code(canvas: DesignCanvas, class_name: str = "GeneratedUI") -> str:
        """生成PyQt5程式碼"""
        code = f'''"""
自動生成的UI程式碼
基於{canvas.__class__.__module__}系統架構
"""
import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QLabel, QPushButton,
    QLineEdit, QTextEdit, QComboBox, QCheckBox, QSpinBox,
    QSlider, QProgressBar, QVBoxLayout, QHBoxLayout
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 導入系統模組
from shared_data import get_data_manager, DeviceType, SensorData
from config import config

class {class_name}(QMainWindow):
    """自動生成的UI類別"""
    
    def __init__(self):
        super().__init__()
        self.data_manager = get_data_manager()
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 創建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 設定窗口標題和大小
        self.setWindowTitle("{class_name}")
        self.setGeometry(100, 100, {canvas.width()}, {canvas.height()})
        
'''
        
        # 生成控件程式碼
        for info in canvas.placed_widgets:
            widget = info['widget']
            widget_type = info['type']
            name = info['name']
            pos = info['position']
            size = info['size']
            
            code += f"        # 創建 {name}\n"
            code += f"        self.{name} = {widget_type}(central_widget)\n"
            code += f"        self.{name}.setGeometry({pos.x()}, {pos.y()}, {size.width()}, {size.height()})\n"
            
            # 添加特定屬性
            if hasattr(widget, 'text') and widget.text():
                code += f"        self.{name}.setText('{widget.text()}')\n"
            
            if hasattr(widget, 'placeholderText') and widget.placeholderText():
                code += f"        self.{name}.setPlaceholderText('{widget.placeholderText()}')\n"
            
            if widget_type == "QComboBox" and widget.count() > 0:
                items = [widget.itemText(i) for i in range(widget.count())]
                code += f"        self.{name}.addItems({items})\n"
            
            code += "\n"
        
        # 添加基本方法
        code += '''    
    def connect_signals(self):
        """連接信號到槽函數"""
        # 在這裡添加信號連接
        pass
    
    def update_data(self):
        """更新數據顯示"""
        # 整合到系統數據流
        try:
            # 從隊列獲取數據
            if not self.data_manager.sensor_queue.empty():
                sensor_data = self.data_manager.sensor_queue.get_nowait()
                # 處理數據更新UI
                pass
        except:
            pass

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = {class_name}()
    window.show()
    sys.exit(app.exec_())
'''
        
        return code

class UIDesigner(QMainWindow):
    """UI設計器主視窗"""
    
    def __init__(self):
        super().__init__()
        self.data_manager = get_data_manager()
        self.init_ui()
        self.current_file = None
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("UI設計器 - WSTC系統")
        self.setGeometry(100, 100, 1400, 800)
        
        # 創建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主佈局
        main_layout = QHBoxLayout()
        
        # 左側控件庫
        self.palette = WidgetPalette()
        self.palette.setMaximumWidth(200)
        
        # 中央設計區域
        canvas_scroll = QScrollArea()
        self.canvas = DesignCanvas()
        canvas_scroll.setWidget(self.canvas)
        canvas_scroll.setWidgetResizable(True)
        
        # 右側屬性編輯器
        self.property_editor = PropertyEditor()
        self.property_editor.setMaximumWidth(300)
        
        # 連接信號
        self.canvas.widget_selected.connect(self.property_editor.show_properties)
        
        # 添加到佈局
        main_layout.addWidget(self.palette)
        main_layout.addWidget(canvas_scroll, 1)
        main_layout.addWidget(self.property_editor)
        
        central_widget.setLayout(main_layout)
        
        # 創建選單欄
        self.create_menu()
        
        # 創建工具欄
        self.create_toolbar()
        
        # 創建狀態欄
        self.statusBar().showMessage("就緒")
    
    def create_menu(self):
        """創建選單欄"""
        menubar = self.menuBar()
        
        # 檔案選單
        file_menu = menubar.addMenu('檔案')
        
        file_menu.addAction('新建', self.new_design)
        file_menu.addAction('開啟', self.open_design)
        file_menu.addAction('儲存', self.save_design)
        file_menu.addAction('另存為', self.save_as_design)
        file_menu.addSeparator()
        file_menu.addAction('生成程式碼', self.generate_code)
        file_menu.addSeparator()
        file_menu.addAction('退出', self.close)
        
        # 編輯選單
        edit_menu = menubar.addMenu('編輯')
        edit_menu.addAction('刪除', self.delete_selected)
        edit_menu.addAction('清空畫布', self.clear_canvas)
        
        # 檢視選單
        view_menu = menubar.addMenu('檢視')
        view_menu.addAction('顯示網格', self.toggle_grid)
        view_menu.addAction('預覽', self.preview_ui)
    
    def create_toolbar(self):
        """創建工具欄"""
        toolbar = self.addToolBar('主工具欄')
        
        toolbar.addAction('新建', self.new_design)
        toolbar.addAction('開啟', self.open_design)
        toolbar.addAction('儲存', self.save_design)
        toolbar.addSeparator()
        toolbar.addAction('刪除', self.delete_selected)
        toolbar.addAction('清空', self.clear_canvas)
        toolbar.addSeparator()
        toolbar.addAction('生成程式碼', self.generate_code)
        toolbar.addAction('預覽', self.preview_ui)
    
    def new_design(self):
        """新建設計"""
        self.canvas.clear_canvas()
        self.current_file = None
        self.statusBar().showMessage("新建設計")
    
    def open_design(self):
        """開啟設計檔案"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "開啟UI設計", "", "JSON檔案 (*.json)"
        )
        if file_path:
            self.load_design(file_path)
    
    def save_design(self):
        """儲存設計"""
        if self.current_file:
            self.save_to_file(self.current_file)
        else:
            self.save_as_design()
    
    def save_as_design(self):
        """另存為設計"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "儲存UI設計", "", "JSON檔案 (*.json)"
        )
        if file_path:
            self.save_to_file(file_path)
            self.current_file = file_path
    
    def save_to_file(self, file_path: str):
        """儲存到檔案"""
        design_data = {
            'canvas_size': [self.canvas.width(), self.canvas.height()],
            'widgets': []
        }
        
        for info in self.canvas.placed_widgets:
            widget = info['widget']
            widget_data = {
                'type': info['type'],
                'name': info['name'],
                'position': [info['position'].x(), info['position'].y()],
                'size': [info['size'].width(), info['size'].height()],
                'properties': {}
            }
            
            # 儲存控件屬性
            if hasattr(widget, 'text'):
                widget_data['properties']['text'] = widget.text()
            if hasattr(widget, 'placeholderText'):
                widget_data['properties']['placeholderText'] = widget.placeholderText()
            
            design_data['widgets'].append(widget_data)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(design_data, f, ensure_ascii=False, indent=2)
            self.statusBar().showMessage(f"已儲存: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"儲存失敗: {e}")
    
    def load_design(self, file_path: str):
        """載入設計檔案"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                design_data = json.load(f)
            
            # 清空當前設計
            self.canvas.clear_canvas()
            
            # 恢復控件
            for widget_data in design_data.get('widgets', []):
                position = QPoint(*widget_data['position'])
                widget = self.canvas.create_widget(widget_data['type'], position)
                
                if widget:
                    # 恢復大小
                    widget.resize(QSize(*widget_data['size']))
                    
                    # 恢復屬性
                    properties = widget_data.get('properties', {})
                    if 'text' in properties and hasattr(widget, 'setText'):
                        widget.setText(properties['text'])
                    if 'placeholderText' in properties and hasattr(widget, 'setPlaceholderText'):
                        widget.setPlaceholderText(properties['placeholderText'])
            
            self.current_file = file_path
            self.statusBar().showMessage(f"已載入: {file_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"載入失敗: {e}")
    
    def delete_selected(self):
        """刪除選中的控件"""
        self.canvas.delete_selected_widget()
        self.statusBar().showMessage("已刪除選中控件")
    
    def clear_canvas(self):
        """清空畫布"""
        reply = QMessageBox.question(
            self, "確認", "確定要清空畫布嗎？",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.canvas.clear_canvas()
            self.statusBar().showMessage("已清空畫布")
    
    def toggle_grid(self):
        """切換網格顯示"""
        self.canvas.show_grid = not self.canvas.show_grid
        self.canvas.update()
        status = "顯示" if self.canvas.show_grid else "隱藏"
        self.statusBar().showMessage(f"網格已{status}")
    
    def generate_code(self):
        """生成程式碼"""
        if not self.canvas.placed_widgets:
            QMessageBox.information(self, "提示", "畫布為空，無法生成程式碼")
            return
        
        class_name, ok = self.get_class_name()
        if not ok:
            return
        
        code = CodeGenerator.generate_ui_code(self.canvas, class_name)
        
        # 顯示程式碼預覽對話框
        self.show_code_preview(code, class_name)
    
    def get_class_name(self):
        """獲取類別名稱"""
        from PyQt5.QtWidgets import QInputDialog
        text, ok = QInputDialog.getText(
            self, "類別名稱", "請輸入UI類別名稱:", text="GeneratedUI"
        )
        return text, ok
    
    def show_code_preview(self, code: str, class_name: str):
        """顯示程式碼預覽"""
        dialog = QDialog(self)
        dialog.setWindowTitle("生成的程式碼")
        dialog.setGeometry(200, 200, 800, 600)
        
        layout = QVBoxLayout()
        
        # 程式碼編輯器
        code_edit = QTextEdit()
        code_edit.setPlainText(code)
        code_edit.setFont(QFont("Consolas", 10))
        layout.addWidget(code_edit)
        
        # 按鈕
        button_layout = QHBoxLayout()
        
        copy_btn = QPushButton("複製到剪貼簿")
        copy_btn.clicked.connect(lambda: QApplication.clipboard().setText(code))
        
        save_btn = QPushButton("儲存到檔案")
        save_btn.clicked.connect(lambda: self.save_generated_code(code, class_name))
        
        close_btn = QPushButton("關閉")
        close_btn.clicked.connect(dialog.accept)
        
        button_layout.addWidget(copy_btn)
        button_layout.addWidget(save_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        dialog.setLayout(layout)
        dialog.exec_()
    
    def save_generated_code(self, code: str, class_name: str):
        """儲存生成的程式碼"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "儲存程式碼", f"{class_name.lower()}.py", "Python檔案 (*.py)"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(code)
                QMessageBox.information(self, "成功", f"程式碼已儲存: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "錯誤", f"儲存失敗: {e}")
    
    def preview_ui(self):
        """預覽UI"""
        if not self.canvas.placed_widgets:
            QMessageBox.information(self, "提示", "畫布為空，無法預覽")
            return
        
        # 創建預覽視窗
        preview = QMainWindow()
        preview.setWindowTitle("UI預覽")
        preview.setGeometry(300, 300, self.canvas.width() + 50, self.canvas.height() + 100)
        
        # 創建預覽控件
        preview_widget = QWidget()
        preview.setCentralWidget(preview_widget)
        
        # 複製畫布控件到預覽視窗
        for info in self.canvas.placed_widgets:
            widget = info['widget']
            widget_type = info['type']
            
            # 創建新控件
            if widget_type == "QLabel":
                new_widget = QLabel(widget.text(), preview_widget)
            elif widget_type == "QPushButton":
                new_widget = QPushButton(widget.text(), preview_widget)
            elif widget_type == "QLineEdit":
                new_widget = QLineEdit(preview_widget)
                new_widget.setText(widget.text())
                new_widget.setPlaceholderText(widget.placeholderText())
            # ... 其他控件類型
            else:
                continue
            
            # 設定位置和大小
            new_widget.setGeometry(
                info['position'].x(), info['position'].y(),
                info['size'].width(), info['size'].height()
            )
            new_widget.show()
        
        preview.show()
        
        # 儲存預覽視窗引用以防止被垃圾回收
        self.preview_window = preview

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設定應用程式屬性
    app.setApplicationName("WSTC UI設計器")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    designer = UIDesigner()
    designer.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()