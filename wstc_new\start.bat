@echo off
echo ========================================
echo WSTC System - Quick Start
echo ========================================
echo.

echo Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    pause
    exit /b 1
)

echo Checking PyQt5...
python -c "import PyQt5" >nul 2>&1
if errorlevel 1 (
    echo Installing PyQt5...
    pip install PyQt5
)

echo.
echo Select mode:
echo 1. Simple mode (recommended)
echo 2. Full mode
echo 3. Test mode
echo.
set /p choice="Choose (1-3): "

if "%choice%"=="1" (
    python main_simple.py
) else if "%choice%"=="2" (
    python main.py
) else if "%choice%"=="3" (
    python tests/test_qt5.py
    pause
    python tests/test_startup_flow.py
) else (
    python main_simple.py
)

pause
