# WSTC 項目結構說明

## 資料夾結構

```
wstc_new/
├── 📁 主要運行程式 (最外層)
│   ├── main.py                 # 主啟動程式 (多進程版本)
│   ├── main_simple.py          # 簡化啟動程式 (線程版本，推薦)
│   ├── main_ui.py              # UI 界面程式
│   ├── sensor_manager.py       # 感測器管理器
│   ├── database_logger.py      # 資料庫記錄器
│   ├── database_sync.py        # 資料庫同步器
│   ├── shared_data.py          # 共享數據管理
│   ├── config.py               # 配置管理
│   ├── start.ps1               # PowerShell 啟動腳本
│   ├── start.bat               # 批次檔啟動腳本
│   ├── start.sh                # Linux 啟動腳本
│   └── requirements.txt        # Python 依賴
│
├── 📁 configs/                 # 配置文件
│   ├── config.ini              # 主配置文件
│   ├── config_demo.ini         # 演示模式配置
│   ├── config_station*.ini     # 各工站配置文件
│   └── requirements-dev.txt    # 開發依賴
│
├── 📁 tools/                   # 工具程式
│   └── config_manager.py       # 配置管理工具
│
├── 📁 utils/                   # 輔助程式
│   └── FA_RDB_OP_integrated.py # 資料庫操作工具
│
├── 📁 ui_designer/             # UI 設計器
│   ├── ui_designer.py          # UI 設計器主程式
│   ├── start_ui_designer.py    # 啟動腳本
│   └── README.md               # 使用說明
│
├── 📁 tests/                   # 測試腳本
│   ├── test_qt5.py             # PyQt5 功能測試
│   ├── test_startup_flow.py    # 啟動流程測試
│   ├── test_database_sync.py   # 資料庫同步測試
│   └── test_device_config.py   # 設備配置測試
│
├── 📁 scripts/                 # 安裝和構建腳本
│   ├── install_dependencies.*  # 依賴安裝腳本
│   ├── build_exe.*             # 打包腳本
│   └── *_system.sh             # 系統服務腳本
│
├── 📁 docs/                    # 文檔和說明
│   ├── QT5_MIGRATION_README.md     # Qt5 遷移說明
│   ├── STARTUP_FLOW_README.md      # 啟動流程說明
│   ├── DEVICE_CONFIG_GUIDE.md      # 設備配置指南
│   ├── DATABASE_SYNC_INTEGRATION.md # 資料庫同步整合
│   └── 其他文檔...
│
└── 📁 legacy/                  # 舊版本文件 (保留備份)
    ├── DB_update.py            # 舊版資料庫同步
    └── insert_ui.py            # 舊版 UI 文件
```

## 文件分類說明

### 🚀 主要運行程式 (根目錄)
這些是系統運行必需的核心文件，放在最外層方便直接執行：

- **main_simple.py** - 推薦的啟動程式（線程版本）
- **main.py** - 完整版啟動程式（多進程版本）
- **main_ui.py** - Qt5 用戶界面
- **sensor_manager.py** - 感測器數據處理
- **database_logger.py** - 資料庫記錄功能
- **shared_data.py** - 進程間數據共享
- **config.py** - 配置文件管理
- **requirements.txt** - Python 依賴清單

### ⚙️ configs/ - 配置文件
系統配置相關文件：

- **config.ini** - 生產環境配置
- **config_demo.ini** - 演示模式配置

### 🧪 tests/ - 測試腳本
測試和驗證相關文件：

- **test_qt5.py** - PyQt5 安裝和功能測試
- **test_startup_flow.py** - 啟動流程模擬測試

### 🔧 scripts/ - 安裝腳本
系統安裝和部署相關腳本：

- **install_dependencies.bat** - Windows 自動安裝腳本
- **install_dependencies.sh** - Linux 自動安裝腳本

### 📚 docs/ - 文檔說明
項目文檔和說明文件：

- **QT5_MIGRATION_README.md** - Qt5 遷移詳細說明
- **STARTUP_FLOW_README.md** - 啟動流程技術文檔
- **CONVERSION_SUMMARY.md** - 轉換工作總結
- **STARTUP_GUIDE.md** - 用戶啟動指南

### 🗄️ legacy/ - 舊版本備份
保留的舊版本文件，用於參考或回滾：

- 舊版本的 tkinter 文件
- 廢棄的配置文件
- 歷史版本備份

## 快速啟動

### 1. 開發/測試環境
```bash
python main_simple.py
```

### 2. 生產環境
```bash
python main.py
```

### 3. 功能測試
```bash
python tests/test_qt5.py
python tests/test_startup_flow.py
```

### 4. 安裝依賴
```bash
# Windows
scripts/install_dependencies.bat

# Linux
chmod +x scripts/install_dependencies.sh
scripts/install_dependencies.sh
```

## 配置管理

### 使用默認配置
系統會自動使用 `configs/config.ini`

### 使用演示配置
```python
# 在代碼中指定
config = Config('configs/config_demo.ini')
```

### 自定義配置
1. 複製 `configs/config.ini` 為新文件
2. 修改配置參數
3. 在啟動時指定配置文件

## 維護建議

### 1. 文件組織
- ✅ 核心程式保持在根目錄
- ✅ 輔助文件分類存放
- ✅ 清晰的資料夾命名

### 2. 版本控制
- 新功能開發在根目錄進行
- 舊版本文件移至 legacy 資料夾
- 重要變更記錄在 docs 中

### 3. 部署流程
- 只需複製根目錄的核心文件
- configs 資料夾包含所有配置
- scripts 資料夾用於環境設置

## 優勢

### 1. 清晰的結構
- 🎯 運行程式一目了然
- 📁 輔助文件分類清楚
- 🔍 易於查找和維護

### 2. 便於部署
- 🚀 核心文件在根目錄
- ⚙️ 配置文件集中管理
- 🧪 測試文件獨立存放

### 3. 易於維護
- 📚 文檔集中在 docs
- 🔧 腳本集中在 scripts
- 🗄️ 舊版本安全保存

---

**整理日期**: 2025-08-01
**結構版本**: 2.0
**狀態**: ✅ 已完成整理
