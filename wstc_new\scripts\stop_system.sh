#!/bin/bash

# 高效能實時感測器系統停止腳本

echo "=========================================="
echo "停止高效能實時感測器系統"
echo "=========================================="

# 檢查是否有運行中的系統
if [ -f "logs/system.pid" ]; then
    system_pid=$(cat logs/system.pid)
    
    # 檢查進程是否還在運行
    if ps -p $system_pid > /dev/null; then
        echo "正在停止系統 (PID: $system_pid)..."
        
        # 優雅停止
        kill -TERM $system_pid
        
        # 等待進程結束
        sleep 3
        
        # 檢查是否已停止
        if ps -p $system_pid > /dev/null; then
            echo "強制停止系統..."
            kill -KILL $system_pid
            sleep 1
        fi
        
        if ps -p $system_pid > /dev/null; then
            echo "✗ 無法停止系統"
            exit 1
        else
            echo "✓ 系統已停止"
            rm -f logs/system.pid
        fi
    else
        echo "系統未運行"
        rm -f logs/system.pid
    fi
else
    # 嘗試通過進程名停止
    echo "嘗試通過進程名停止..."
    pkill -f "main.py"
    sleep 2
    
    if pgrep -f "main.py" > /dev/null; then
        echo "強制停止所有相關進程..."
        pkill -9 -f "main.py"
        pkill -9 -f "sensor_manager.py"
        pkill -9 -f "database_logger.py"
        pkill -9 -f "main_ui.py"
    fi
    
    echo "✓ 系統已停止"
fi

echo "=========================================="
echo "停止完成"
echo "=========================================="