#!/usr/bin/env python3
"""
測試 PyQt5 安裝和基本功能
"""
import sys

def test_pyqt5_import():
    """測試 PyQt5 導入"""
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QPushButton
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        print("✓ PyQt5 導入成功")
        return True
    except ImportError as e:
        print(f"✗ PyQt5 導入失敗: {e}")
        return False

def test_pyqt5_basic_window():
    """測試基本視窗創建"""
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        window = QMainWindow()
        window.setWindowTitle("PyQt5 測試視窗")
        window.setGeometry(100, 100, 400, 300)
        
        label = QLabel("PyQt5 測試成功！", window)
        label.setAlignment(Qt.AlignCenter)
        label.setGeometry(50, 100, 300, 50)
        
        window.setCentralWidget(label)
        
        print("✓ PyQt5 基本視窗創建成功")
        print("注意：視窗已創建但未顯示（避免阻塞測試）")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"✗ PyQt5 視窗創建失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("=" * 50)
    print("PyQt5 功能測試")
    print("=" * 50)
    
    # 測試導入
    import_ok = test_pyqt5_import()
    
    if import_ok:
        # 測試基本功能
        window_ok = test_pyqt5_basic_window()
        
        if window_ok:
            print("\n✓ 所有 PyQt5 測試通過！")
            print("可以開始使用 Qt5 版本的 UI")
            return True
    
    print("\n✗ PyQt5 測試失敗")
    print("請檢查 PyQt5 安裝：")
    print("  Windows: pip install PyQt5")
    print("  Ubuntu/Debian: sudo apt-get install python3-pyqt5")
    print("  CentOS/RHEL: sudo yum install python3-qt5")
    print("  Fedora: sudo dnf install python3-qt5")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
